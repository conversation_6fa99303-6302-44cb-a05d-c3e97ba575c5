"""
Pruebas específicas para el procesador de texto
"""

from procesador_texto import ProcesadorTexto, procesar_ejemplo_imagen

def test_ejemplo_imagen():
    """Prueba el ejemplo específico de la imagen"""
    print("=== PRUEBA DEL EJEMPLO DE LA IMAGEN ===")
    
    texto_problema = """
    Un protón con una velocidad de v = (2 × 10⁵) î m/s se mueve en una región donde hay un campo 
    eléctrico E = (3 × 10³) ĵ V/m y un campo magnético B = (0.02) k̂ T. Calcula la fuerza eléctrica, la 
    fuerza magnética, la fuerza total que actúa sobre el protón, la aceleración resultante, determina el tipo de 
    movimiento que describe la partícula y, en el caso de que se elimine el campo eléctrico, calcula el radio de la 
    trayectoria circular que seguiría. Usa los valores q = 1.6 × 10⁻¹⁹ C y m = 1.67 × 10⁻²⁷ kg.
    """
    
    procesador = ProcesadorTexto()
    resultado = procesador.procesar_problema(texto_problema)
    
    if 'error' in resultado:
        print(f"❌ Error: {resultado['error']}")
        return False
    
    # Verificar datos extraídos
    datos = resultado['datos_extraidos']
    particula = resultado['particula']
    campo = resultado['campo']
    
    print("✓ Problema procesado correctamente")
    print(f"Tipo de partícula: {datos['tipo_particula']}")
    print(f"Velocidad: {particula.velocidad}")
    print(f"Campo E: {campo.E}")
    print(f"Campo B: {campo.B}")
    print(f"Preguntas: {resultado['preguntas']}")
    
    # Verificar valores esperados
    assert datos['tipo_particula'] == 'proton', "Tipo de partícula incorrecto"
    assert abs(particula.velocidad.x - 2e5) < 1e3, "Velocidad X incorrecta"
    assert abs(campo.E.y - 3e3) < 1, "Campo E Y incorrecto"
    assert abs(campo.B.z - 0.02) < 1e-4, "Campo B Z incorrecto"
    
    print("✅ Todos los valores extraídos correctamente")
    return True

def test_diferentes_formatos():
    """Prueba diferentes formatos de entrada"""
    print("\n=== PRUEBA DE DIFERENTES FORMATOS ===")
    
    procesador = ProcesadorTexto()
    
    # Formato 1: Notación científica
    texto1 = "Un electrón con velocidad v = 1.5e6 m/s en campo B = 2e-3 T"
    resultado1 = procesador.procesar_problema(texto1)
    print("✓ Formato científico procesado")
    
    # Formato 2: Sin vectores unitarios
    texto2 = "Protón con velocidad 200000 m/s, campo eléctrico 5000 V/m"
    resultado2 = procesador.procesar_problema(texto2)
    print("✓ Formato simple procesado")
    
    # Formato 3: Con paréntesis
    texto3 = "Partícula con v = (1e5, 2e5, 0) m/s y E = (0, 1000, 500) V/m"
    resultado3 = procesador.procesar_problema(texto3)
    print("✓ Formato con paréntesis procesado")
    
    return True

def test_casos_especiales():
    """Prueba casos especiales y límite"""
    print("\n=== PRUEBA DE CASOS ESPECIALES ===")
    
    procesador = ProcesadorTexto()
    
    # Caso 1: Solo campo eléctrico
    texto1 = "Electrón en campo eléctrico E = 1000 V/m, velocidad inicial 1e6 m/s"
    resultado1 = procesador.procesar_problema(texto1)
    assert resultado1['campo'].B.magnitude() == 0, "Campo B debería ser cero"
    print("✓ Solo campo eléctrico")
    
    # Caso 2: Solo campo magnético
    texto2 = "Protón en campo magnético B = 0.1 T, velocidad 1e5 m/s"
    resultado2 = procesador.procesar_problema(texto2)
    assert resultado2['campo'].E.magnitude() == 0, "Campo E debería ser cero"
    print("✓ Solo campo magnético")
    
    # Caso 3: Texto sin datos suficientes
    texto3 = "Una partícula se mueve"
    resultado3 = procesador.procesar_problema(texto3)
    print("✓ Texto incompleto manejado")
    
    return True

def test_calculo_completo():
    """Prueba un cálculo completo con el ejemplo de la imagen"""
    print("\n=== PRUEBA DE CÁLCULO COMPLETO ===")
    
    from lorentz_physics import fuerza_lorentz, fuerza_electrica, fuerza_magnetica
    from lorentz_physics import radio_curvatura_magnetico
    
    resultado = procesar_ejemplo_imagen()
    
    if 'error' in resultado:
        print(f"❌ Error: {resultado['error']}")
        return False
    
    particula = resultado['particula']
    campo = resultado['campo']
    
    # Calcular fuerzas
    F_E = fuerza_electrica(particula, campo.E)
    F_B = fuerza_magnetica(particula, campo.B)
    F_total = fuerza_lorentz(particula, campo)
    
    print("Resultados del cálculo:")
    print(f"Fuerza eléctrica: {F_E}")
    print(f"Fuerza magnética: {F_B}")
    print(f"Fuerza total: {F_total}")
    
    # Verificar que las fuerzas son correctas
    # F_E = qE = (1.6e-19) * (3e3) ĵ = 4.8e-16 ĵ N
    assert abs(F_E.y - 4.8e-16) < 1e-18, f"Fuerza eléctrica incorrecta: {F_E.y}"
    
    # F_B = q(v × B) = q * vx * Bz * (-ĵ) = (1.6e-19) * (2e5) * (0.02) * (-ĵ)
    assert abs(F_B.y + 6.4e-16) < 1e-18, f"Fuerza magnética incorrecta: {F_B.y}"
    
    print("✅ Fuerzas calculadas correctamente")
    
    # Radio sin campo eléctrico
    radio = radio_curvatura_magnetico(particula, campo.B)
    print(f"Radio sin campo E: {radio:.3e} m")
    
    # Verificar radio: r = mv/(qB) = (1.67e-27 * 2e5) / (1.6e-19 * 0.02)
    radio_esperado = (1.67e-27 * 2e5) / (1.6e-19 * 0.02)
    assert abs(radio - radio_esperado) < 1e-6, f"Radio incorrecto: {radio} vs {radio_esperado}"
    
    print("✅ Radio calculado correctamente")
    return True

def test_reconocimiento_preguntas():
    """Prueba el reconocimiento de preguntas en el texto"""
    print("\n=== PRUEBA DE RECONOCIMIENTO DE PREGUNTAS ===")
    
    procesador = ProcesadorTexto()
    
    textos_preguntas = [
        ("Calcula la fuerza eléctrica", ['fuerza_electrica']),
        ("Determina la fuerza magnética y el radio", ['fuerza_magnetica', 'radio']),
        ("Encuentra la aceleración y el período", ['aceleracion', 'periodo']),
        ("Analiza el movimiento y la trayectoria", ['trayectoria']),
        ("Calcula la energía y el trabajo", ['energia', 'trabajo'])
    ]
    
    for texto, preguntas_esperadas in textos_preguntas:
        texto_completo = f"Un electrón se mueve. {texto}."
        resultado = procesador.procesar_problema(texto_completo)
        preguntas_encontradas = resultado['preguntas']
        
        for pregunta in preguntas_esperadas:
            assert pregunta in preguntas_encontradas, f"Pregunta '{pregunta}' no encontrada en '{texto}'"
        
        print(f"✓ '{texto}' → {preguntas_encontradas}")
    
    print("✅ Reconocimiento de preguntas correcto")
    return True

def main():
    """Ejecuta todas las pruebas del procesador de texto"""
    print("PRUEBAS DEL PROCESADOR DE TEXTO")
    print("=" * 50)
    
    pruebas = [
        ("Ejemplo de la imagen", test_ejemplo_imagen),
        ("Diferentes formatos", test_diferentes_formatos),
        ("Casos especiales", test_casos_especiales),
        ("Cálculo completo", test_calculo_completo),
        ("Reconocimiento de preguntas", test_reconocimiento_preguntas)
    ]
    
    exitosas = 0
    
    for nombre, prueba in pruebas:
        try:
            print(f"\n--- {nombre} ---")
            if prueba():
                exitosas += 1
                print(f"✅ {nombre}: EXITOSA")
            else:
                print(f"❌ {nombre}: FALLIDA")
        except Exception as e:
            print(f"❌ {nombre}: ERROR - {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"RESULTADO: {exitosas}/{len(pruebas)} pruebas exitosas")
    
    if exitosas == len(pruebas):
        print("\n🎉 ¡Todas las pruebas del procesador de texto pasaron!")
        print("El sistema de entrada por texto está funcionando correctamente.")
    else:
        print(f"\n⚠ {len(pruebas) - exitosas} pruebas fallaron.")
    
    return exitosas == len(pruebas)

if __name__ == "__main__":
    main()
