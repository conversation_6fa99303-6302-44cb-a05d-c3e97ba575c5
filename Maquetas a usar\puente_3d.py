import bpy
import math
import random

# Limpiar escena
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete()

# Configurar renderizado
bpy.context.scene.render.engine = 'CYCLES'
bpy.context.scene.cycles.device = 'GPU'
bpy.context.scene.cycles.samples = 128

# Crear carretera debajo del puente
bpy.ops.mesh.primitive_plane_add(size=100, enter_editmode=False, align='WORLD', location=(0, 0, 0))
carretera_inferior = bpy.context.active_object
carretera_inferior.name = "Carretera_Inferior"

# Material para la carretera (asfalto)
mat_carretera = bpy.data.materials.new(name="Material_Carretera_Inferior")
mat_carretera.use_nodes = True
nodes = mat_carretera.node_tree.nodes
bsdf = nodes.get("Principled BSDF")
bsdf.inputs[0].default_value = (0.1, 0.1, 0.1, 1.0)  # Negro asfalto
bsdf.inputs[7].default_value = 0.9  # Rugosidad
carretera_inferior.data.materials.append(mat_carretera)

# Crear líneas centrales de la carretera
bpy.ops.mesh.primitive_plane_add(size=1, enter_editmode=False, align='WORLD', location=(0, 0, 0.01))
linea_central = bpy.context.active_object
linea_central.name = "Linea_Central_Inferior"
linea_central.scale = (50, 0.3, 1)

# Material para líneas (amarillo)
mat_linea = bpy.data.materials.new(name="Material_Linea_Central")
mat_linea.use_nodes = True
nodes = mat_linea.node_tree.nodes
bsdf = nodes.get("Principled BSDF")
bsdf.inputs[0].default_value = (1.0, 0.8, 0.0, 1.0)  # Amarillo
bsdf.inputs[7].default_value = 0.3  # Rugosidad
linea_central.data.materials.append(mat_linea)

# Crear carriles laterales
for offset in [-3, 3]:
    bpy.ops.mesh.primitive_plane_add(size=1, enter_editmode=False, align='WORLD', location=(0, offset, 0.01))
    carril = bpy.context.active_object
    carril.name = f"Carril_{offset}"
    carril.scale = (50, 0.2, 1)

    # Material para carriles (blanco)
    mat_carril = bpy.data.materials.new(name=f"Material_Carril_{offset}")
    mat_carril.use_nodes = True
    nodes = mat_carril.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (1.0, 1.0, 1.0, 1.0)  # Blanco
    bsdf.inputs[7].default_value = 0.3  # Rugosidad
    carril.data.materials.append(mat_carril)

# Crear orillas
def crear_orilla(posicion_x, ancho=40, largo=10):
    bpy.ops.mesh.primitive_plane_add(size=1, enter_editmode=False, align='WORLD', 
                                   location=(posicion_x, 0, 0.1))
    orilla = bpy.context.active_object
    orilla.name = f"Orilla_{posicion_x}"
    orilla.scale = (largo, ancho, 1)
    
    # Material para la orilla (tierra/arena)
    mat_orilla = bpy.data.materials.new(name=f"Material_Orilla_{posicion_x}")
    mat_orilla.use_nodes = True
    nodes = mat_orilla.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.8, 0.7, 0.5, 1.0)  # Color arena
    bsdf.inputs[7].default_value = 0.7  # Rugosidad
    orilla.data.materials.append(mat_orilla)
    
    return orilla

# Crear orillas a ambos lados del río
orilla_izquierda = crear_orilla(-40)
orilla_derecha = crear_orilla(40)

# Función para crear pilares del puente
def crear_pilar(posicion_x, altura=15, radio_base=2, radio_superior=1.5):
    # Base del pilar
    bpy.ops.mesh.primitive_cylinder_add(radius=radio_base, depth=altura, 
                                      enter_editmode=False, 
                                      location=(posicion_x, 0, altura/2))
    pilar = bpy.context.active_object
    pilar.name = f"Pilar_{posicion_x}"
    
    # Dar forma cónica al pilar
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.select_all(action='DESELECT')
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Seleccionar vértices superiores
    for v in pilar.data.vertices:
        if v.co.z > 0:
            v.select = True
    
    # Escalar los vértices superiores
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.transform.resize(value=(radio_superior/radio_base, radio_superior/radio_base, 1))
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Material para el pilar (concreto)
    mat_pilar = bpy.data.materials.new(name=f"Material_Pilar_{posicion_x}")
    mat_pilar.use_nodes = True
    nodes = mat_pilar.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.7, 0.7, 0.7, 1.0)  # Gris claro
    bsdf.inputs[7].default_value = 0.5  # Rugosidad
    pilar.data.materials.append(mat_pilar)
    
    return pilar

# Crear pilares del puente
pilares = []
distancia_entre_pilares = 20
for i in range(-2, 3):
    posicion_x = i * distancia_entre_pilares
    pilar = crear_pilar(posicion_x)
    pilares.append(pilar)

# Función para crear el tablero del puente
def crear_tablero(longitud=100, ancho=22, grosor=1):
    bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False,
                                  location=(0, 0, 15 + grosor/2))
    tablero = bpy.context.active_object
    tablero.name = "Tablero_Puente"
    tablero.scale = (longitud/2, ancho/2, grosor/2)
    
    # Material para el tablero (asfalto)
    mat_tablero = bpy.data.materials.new(name="Material_Tablero")
    mat_tablero.use_nodes = True
    nodes = mat_tablero.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.1, 0.1, 0.1, 1.0)  # Negro
    bsdf.inputs[7].default_value = 0.7  # Rugosidad
    tablero.data.materials.append(mat_tablero)
    
    return tablero

# Crear el tablero principal del puente
tablero = crear_tablero()

# Función para crear barandillas (integradas al tablero)
def crear_barandilla(longitud=100, altura=1.5, espaciado=2):
    # Crear postes
    postes = []
    num_postes = int(longitud / espaciado) + 1

    for i in range(num_postes):
        # Posición x a lo largo del puente
        pos_x = -longitud/2 + i * espaciado

        # Crear poste izquierdo (empotrado en el tablero)
        bpy.ops.mesh.primitive_cylinder_add(radius=0.2, depth=altura + 0.5,
                                          enter_editmode=False,
                                          location=(pos_x, 9.8, 15 + altura/2 - 0.25))
        poste_izq = bpy.context.active_object
        poste_izq.name = f"Poste_Izquierdo_{i}"
        postes.append(poste_izq)

        # Crear poste derecho (empotrado en el tablero)
        bpy.ops.mesh.primitive_cylinder_add(radius=0.2, depth=altura + 0.5,
                                          enter_editmode=False,
                                          location=(pos_x, -9.8, 15 + altura/2 - 0.25))
        poste_der = bpy.context.active_object
        poste_der.name = f"Poste_Derecho_{i}"
        postes.append(poste_der)
    
    # Crear cables horizontales (integrados a los postes)
    cables = []
    for j in range(3):  # 3 cables horizontales
        altura_cable = 15 + (j + 1) * altura/3

        # Cable izquierdo (conectado a los postes)
        bpy.ops.mesh.primitive_cylinder_add(radius=0.1, depth=longitud,
                                          enter_editmode=False,
                                          location=(0, 9.8, altura_cable))
        cable_izq = bpy.context.active_object
        cable_izq.name = f"Cable_Izquierdo_{j}"
        cable_izq.rotation_euler.y = math.pi/2
        cables.append(cable_izq)

        # Cable derecho (conectado a los postes)
        bpy.ops.mesh.primitive_cylinder_add(radius=0.1, depth=longitud,
                                          enter_editmode=False,
                                          location=(0, -9.8, altura_cable))
        cable_der = bpy.context.active_object
        cable_der.name = f"Cable_Derecho_{j}"
        cable_der.rotation_euler.y = math.pi/2
        cables.append(cable_der)
    
    # Material para barandillas (metal)
    mat_barandilla = bpy.data.materials.new(name="Material_Barandilla")
    mat_barandilla.use_nodes = True
    nodes = mat_barandilla.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.8, 0.8, 0.8, 1.0)  # Gris claro
    bsdf.inputs[4].default_value = 0.9  # Metálico
    bsdf.inputs[7].default_value = 0.2  # Rugosidad
    
    # Aplicar material a todos los elementos
    for elemento in postes + cables:
        elemento.data.materials.append(mat_barandilla)
    
    return postes, cables

# Crear barandillas
postes, cables = crear_barandilla()

# Función para crear arcos entre pilares
def crear_arcos():
    arcos = []
    
    for i in range(len(pilares) - 1):
        pilar_actual = pilares[i]
        pilar_siguiente = pilares[i+1]
        
        # Obtener posiciones
        pos_x1 = pilar_actual.location.x
        pos_x2 = pilar_siguiente.location.x
        centro_x = (pos_x1 + pos_x2) / 2
        distancia = abs(pos_x2 - pos_x1)
        
        # Crear curva para el arco
        curva = bpy.data.curves.new('ArcoData', 'CURVE')
        curva.dimensions = '3D'
        
        spline = curva.splines.new('BEZIER')
        spline.bezier_points.add(1)  # Necesitamos 2 puntos para un arco simple
        
        # Punto inicial
        spline.bezier_points[0].co = (pos_x1, 0, 5)
        spline.bezier_points[0].handle_left = (pos_x1, 0, 5)
        spline.bezier_points[0].handle_right = (pos_x1 + distancia/4, 0, 0)
        
        # Punto final
        spline.bezier_points[1].co = (pos_x2, 0, 5)
        spline.bezier_points[1].handle_left = (pos_x2 - distancia/4, 0, 0)
        spline.bezier_points[1].handle_right = (pos_x2, 0, 5)
        
        # Crear objeto a partir de la curva
        obj_arco = bpy.data.objects.new(f'Arco_{i}', curva)
        bpy.context.collection.objects.link(obj_arco)
        
        # Dar volumen al arco
        curva.bevel_depth = 1.0
        curva.bevel_resolution = 6
        
        # Material para el arco
        mat_arco = bpy.data.materials.new(name=f"Material_Arco_{i}")
        mat_arco.use_nodes = True
        nodes = mat_arco.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        bsdf.inputs[0].default_value = (0.7, 0.7, 0.7, 1.0)  # Gris claro
        bsdf.inputs[7].default_value = 0.5  # Rugosidad
        obj_arco.data.materials.append(mat_arco)
        
        arcos.append(obj_arco)
    
    return arcos

# Crear arcos
arcos = crear_arcos()

# Función para crear carreteras en las orillas
def crear_carretera(orilla, ancho=8):
    # Obtener dimensiones de la orilla
    largo_orilla = orilla.scale.x * 2
    ancho_orilla = orilla.scale.y * 2
    pos_x = orilla.location.x
    
    # Crear carretera
    bpy.ops.mesh.primitive_plane_add(size=1, enter_editmode=False, 
                                   location=(pos_x, 0, 0.2))
    carretera = bpy.context.active_object
    carretera.name = f"Carretera_{pos_x}"
    carretera.scale = (largo_orilla, ancho/2, 1)
    
    # Material para la carretera (asfalto)
    mat_carretera = bpy.data.materials.new(name=f"Material_Carretera_{pos_x}")
    mat_carretera.use_nodes = True
    nodes = mat_carretera.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.1, 0.1, 0.1, 1.0)  # Negro
    bsdf.inputs[7].default_value = 0.7  # Rugosidad
    carretera.data.materials.append(mat_carretera)
    
    # Crear líneas en la carretera
    bpy.ops.mesh.primitive_plane_add(size=1, enter_editmode=False, 
                                   location=(pos_x, 0, 0.21))
    linea = bpy.context.active_object
    linea.name = f"Linea_{pos_x}"
    linea.scale = (largo_orilla, 0.1, 1)
    
    # Material para la línea (blanco)
    mat_linea = bpy.data.materials.new(name=f"Material_Linea_{pos_x}")
    mat_linea.use_nodes = True
    nodes = mat_linea.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (1.0, 1.0, 1.0, 1.0)  # Blanco
    bsdf.inputs[7].default_value = 0.3  # Rugosidad
    linea.data.materials.append(mat_linea)
    
    return carretera, linea

# Crear carreteras en ambas orillas
carretera_izq, linea_izq = crear_carretera(orilla_izquierda)
carretera_der, linea_der = crear_carretera(orilla_derecha)

# Añadir vegetación en las orillas
def crear_vegetacion(orilla, cantidad=30):
    # Obtener dimensiones y posición de la orilla
    largo_orilla = orilla.scale.x * 2
    ancho_orilla = orilla.scale.y * 2
    pos_x = orilla.location.x
    
    # Crear árboles y arbustos
    for _ in range(cantidad):
        # Posición aleatoria dentro de la orilla, evitando la carretera
        offset_x = random.uniform(-largo_orilla/2, largo_orilla/2)
        offset_y = random.uniform(-ancho_orilla/2, ancho_orilla/2)
        if abs(offset_y) < 5:  # Evitar la carretera
            continue
        
        # Decidir si crear árbol o arbusto
        if random.random() > 0.5:  # Árbol
            # Tronco
            altura = random.uniform(3, 7)
            bpy.ops.mesh.primitive_cylinder_add(radius=0.3, depth=altura, 
                                              enter_editmode=False, 
                                              location=(pos_x + offset_x, offset_y, altura/2))
            tronco = bpy.context.active_object
            tronco.name = f"Tronco_{pos_x}_{offset_x}_{offset_y}"
            
            # Material para tronco
            mat_tronco = bpy.data.materials.new(name=f"Material_Tronco_{pos_x}_{offset_x}_{offset_y}")
            mat_tronco.use_nodes = True
            nodes = mat_tronco.node_tree.nodes
            bsdf = nodes.get("Principled BSDF")
            bsdf.inputs[0].default_value = (0.3, 0.2, 0.1, 1.0)  # Marrón
            tronco.data.materials.append(mat_tronco)
            
            # Copa del árbol
            bpy.ops.mesh.primitive_ico_sphere_add(radius=random.uniform(1.5, 2.5), 
                                                enter_editmode=False, 
                                                location=(pos_x + offset_x, offset_y, altura + 1))
            copa = bpy.context.active_object
            copa.name = f"Copa_{pos_x}_{offset_x}_{offset_y}"
            
            # Material para copa
            mat_copa = bpy.data.materials.new(name=f"Material_Copa_{pos_x}_{offset_x}_{offset_y}")
            mat_copa.use_nodes = True
            nodes = mat_copa.node_tree.nodes
            bsdf = nodes.get("Principled BSDF")
            verde = random.uniform(0.2, 0.4)
            bsdf.inputs[0].default_value = (0.1, verde, 0.1, 1.0)  # Verde variado
            copa.data.materials.append(mat_copa)
        else:  # Arbusto
            bpy.ops.mesh.primitive_ico_sphere_add(radius=random.uniform(0.5, 1.2), 
                                                enter_editmode=False, 
                                                location=(pos_x + offset_x, offset_y, 0.7))
            arbusto = bpy.context.active_object
            arbusto.name = f"Arbusto_{pos_x}_{offset_x}_{offset_y}"
            
            # Material para arbusto
            mat_arbusto = bpy.data.materials.new(name=f"Material_Arbusto_{pos_x}_{offset_x}_{offset_y}")
            mat_arbusto.use_nodes = True
            nodes = mat_arbusto.node_tree.nodes
            bsdf = nodes.get("Principled BSDF")
            verde = random.uniform(0.2, 0.5)
            bsdf.inputs[0].default_value = (0.1, verde, 0.1, 1.0)  # Verde variado
            arbusto.data.materials.append(mat_arbusto)

# Añadir vegetación a ambas orillas
crear_vegetacion(orilla_izquierda)
crear_vegetacion(orilla_derecha)

# Añadir iluminación
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 50))
sol = bpy.context.active_object
sol.name = "Sol"
sol.data.energy = 5
sol.rotation_euler = (math.radians(60), 0, math.radians(30))

# Añadir cámara
bpy.ops.object.camera_add(enter_editmode=False, align='VIEW', location=(50, -50, 30), rotation=(math.radians(60), 0, math.radians(45)))
camara = bpy.context.active_object
camara.name = "Camara_Puente"
bpy.context.scene.camera = camara

# Configurar fondo de cielo
world = bpy.context.scene.world
world.use_nodes = True
bg = world.node_tree.nodes['Background']
bg.inputs[0].default_value = (0.5, 0.7, 1.0, 1.0)  # Azul cielo
bg.inputs[1].default_value = 1.0  # Intensidad

print("¡Maqueta del puente creada con éxito!")
print("Para renderizar, presiona F12 o usa el menú Render > Render Image")
