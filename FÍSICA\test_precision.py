"""
Pruebas de precisión y validación para la calculadora mejorada
"""

import math
import sys
import os

# Agregar el directorio actual al path para importar los módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_precision_numerica():
    """Prueba la precisión numérica mejorada"""
    print("🔬 PRUEBA DE PRECISIÓN NUMÉRICA")
    print("=" * 40)
    
    try:
        from lorentz_mejorado import Vector3D, CalculadoraLorentzMejorada
        
        # Test 1: Precisión en vectores
        v1 = Vector3D(2e5, 0, 0)
        v2 = Vector3D(0, 0, 0.02)
        
        # Producto cruz debe ser preciso
        cross = v1.cross(v2)
        expected = Vector3D(0, -4000, 0)
        
        print(f"v1 = {v1}")
        print(f"v2 = {v2}")
        print(f"v1 × v2 = {cross}")
        print(f"Esperado: {expected}")
        
        # Verificar precisión
        error = abs(cross.y - expected.y)
        print(f"Error en componente Y: {error:.2e}")
        
        if error < 1e-12:
            print("✅ Precisión vectorial correcta")
        else:
            print("❌ Error de precisión vectorial")
            
        return error < 1e-12
        
    except Exception as e:
        print(f"❌ Error en prueba de precisión: {e}")
        return False

def test_validacion_unidades():
    """Prueba la validación de unidades"""
    print("\n🔍 PRUEBA DE VALIDACIÓN DE UNIDADES")
    print("=" * 40)
    
    try:
        from lorentz_mejorado import ValidadorUnidades
        
        validador = ValidadorUnidades()
        
        # Test conversiones
        tests = [
            ("Carga elemental", validador.validar_carga, 1, "e", 1.602176634e-19),
            ("Velocidad km/s", validador.validar_velocidad, 100, "km/s", 100000),
            ("Campo magnético mT", validador.validar_campo_magnetico, 20, "mT", 0.02),
            ("Masa en u", validador.validar_masa, 1, "u", 1.66053906660e-27)
        ]
        
        for nombre, func, valor, unidad, esperado in tests:
            resultado = func(valor, unidad)
            error_relativo = abs(resultado - esperado) / esperado
            
            print(f"{nombre}: {valor} {unidad} → {resultado:.6e}")
            print(f"Esperado: {esperado:.6e}, Error: {error_relativo:.2e}")
            
            if error_relativo < 1e-10:
                print("✅ Conversión correcta")
            else:
                print("❌ Error en conversión")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error en validación de unidades: {e}")
        return False

def test_componentes_artificiales():
    """Prueba que no se generen componentes artificiales"""
    print("\n🚫 PRUEBA DE COMPONENTES ARTIFICIALES")
    print("=" * 40)
    
    try:
        from lorentz_mejorado import Vector3D
        
        # Caso: Campo E solo en Y, debe generar fuerza solo en Y
        E = Vector3D(0, 3000, 0)  # Solo componente Y
        q = 1.602176634e-19
        
        F_E = E * q
        
        print(f"Campo E: {E}")
        print(f"Fuerza F_E = qE: {F_E}")
        
        # Verificar que no hay componentes artificiales
        if abs(F_E.x) < 1e-20 and abs(F_E.z) < 1e-20:
            print("✅ No hay componentes artificiales en X y Z")
        else:
            print("❌ Componentes artificiales detectadas")
            return False
        
        # Verificar componente Y correcta
        F_E_y_esperada = q * 3000
        error = abs(F_E.y - F_E_y_esperada)
        
        if error < 1e-25:
            print("✅ Componente Y correcta")
        else:
            print(f"❌ Error en componente Y: {error:.2e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba de componentes: {e}")
        return False

def test_producto_cruz_precision():
    """Prueba la precisión del producto cruz"""
    print("\n⚡ PRUEBA DE PRODUCTO CRUZ")
    print("=" * 40)
    
    try:
        from lorentz_mejorado import Vector3D
        
        # Caso del ejemplo: v = (2e5, 0, 0), B = (0, 0, 0.02)
        v = Vector3D(2e5, 0, 0)
        B = Vector3D(0, 0, 0.02)
        
        cross = v.cross(B)
        
        print(f"v = {v}")
        print(f"B = {B}")
        print(f"v × B = {cross}")
        
        # Resultado esperado: (0, -4000, 0)
        expected_y = -2e5 * 0.02  # = -4000
        
        print(f"Componente Y esperada: {expected_y}")
        print(f"Componente Y calculada: {cross.y}")
        
        error = abs(cross.y - expected_y)
        print(f"Error absoluto: {error:.2e}")
        
        # Verificar precisión
        if error < 1e-10:
            print("✅ Producto cruz preciso")
        else:
            print("❌ Error en producto cruz")
            return False
        
        # Verificar que otras componentes son cero
        if abs(cross.x) < 1e-15 and abs(cross.z) < 1e-15:
            print("✅ Componentes X y Z correctamente cero")
        else:
            print("❌ Componentes X o Z no son cero")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error en producto cruz: {e}")
        return False

def test_radio_curvatura():
    """Prueba el cálculo preciso del radio de curvatura"""
    print("\n🔵 PRUEBA DE RADIO DE CURVATURA")
    print("=" * 40)
    
    try:
        from lorentz_mejorado import Vector3D, CalculadoraLorentzMejorada
        
        calc = CalculadoraLorentzMejorada()
        
        # Datos del ejemplo
        m = 1.67262192369e-27  # masa protón
        v = Vector3D(2e5, 0, 0)  # velocidad
        q = 1.602176634e-19    # carga
        B = Vector3D(0, 0, 0.02)  # campo magnético
        
        radio = calc.calcular_radio_preciso(m, v, q, B)
        
        # Cálculo manual para verificar
        v_mag = v.magnitude()
        B_mag = B.magnitude()
        radio_esperado = (m * v_mag) / (abs(q) * B_mag)
        
        print(f"Masa: {m:.6e} kg")
        print(f"Velocidad: {v_mag:.6e} m/s")
        print(f"Carga: {q:.6e} C")
        print(f"Campo B: {B_mag:.6e} T")
        print(f"Radio calculado: {radio:.12e} m")
        print(f"Radio esperado: {radio_esperado:.12e} m")
        print(f"Radio en cm: {radio*100:.6f} cm")
        
        error_relativo = abs(radio - radio_esperado) / radio_esperado
        print(f"Error relativo: {error_relativo:.2e}")
        
        if error_relativo < 1e-14:
            print("✅ Radio de curvatura preciso")
        else:
            print("❌ Error en radio de curvatura")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error en radio de curvatura: {e}")
        return False

def test_ortogonalidad_fuerzas():
    """Prueba que F_B sea perpendicular a v y B"""
    print("\n⊥ PRUEBA DE ORTOGONALIDAD")
    print("=" * 40)
    
    try:
        from lorentz_mejorado import Vector3D
        
        # Datos del ejemplo
        v = Vector3D(2e5, 0, 0)
        B = Vector3D(0, 0, 0.02)
        q = 1.602176634e-19
        
        # Calcular fuerza magnética
        v_cross_B = v.cross(B)
        F_B = v_cross_B * q
        
        print(f"v = {v}")
        print(f"B = {B}")
        print(f"F_B = {F_B}")
        
        # Verificar ortogonalidad
        dot_FB_v = F_B.dot(v)
        dot_FB_B = F_B.dot(B)
        
        print(f"F_B · v = {dot_FB_v:.2e} (debe ser ≈ 0)")
        print(f"F_B · B = {dot_FB_B:.2e} (debe ser ≈ 0)")
        
        if abs(dot_FB_v) < 1e-12 and abs(dot_FB_B) < 1e-12:
            print("✅ Ortogonalidad verificada")
            return True
        else:
            print("❌ Falla en ortogonalidad")
            return False
        
    except Exception as e:
        print(f"❌ Error en ortogonalidad: {e}")
        return False

def test_ejemplo_completo():
    """Prueba el ejemplo completo de la imagen"""
    print("\n🎯 PRUEBA DEL EJEMPLO COMPLETO")
    print("=" * 40)
    
    try:
        from lorentz_mejorado import Vector3D
        
        # Datos exactos del ejemplo
        q = 1.602176634e-19    # Carga protón
        m = 1.67262192369e-27  # Masa protón
        v = Vector3D(2e5, 0, 0)      # Velocidad
        E = Vector3D(0, 3e3, 0)      # Campo eléctrico
        B = Vector3D(0, 0, 0.02)     # Campo magnético
        
        print("DATOS:")
        print(f"q = {q:.12e} C")
        print(f"m = {m:.12e} kg")
        print(f"v = {v}")
        print(f"E = {E}")
        print(f"B = {B}")
        
        # Cálculos
        F_E = E * q
        v_cross_B = v.cross(B)
        F_B = v_cross_B * q
        F_total = F_E + F_B
        
        print(f"\nRESULTADOS:")
        print(f"F_E = {F_E}")
        print(f"|F_E| = {F_E.magnitude():.6e} N")
        print(f"F_B = {F_B}")
        print(f"|F_B| = {F_B.magnitude():.6e} N")
        print(f"F_total = {F_total}")
        print(f"|F_total| = {F_total.magnitude():.6e} N")
        
        # Verificar valores esperados
        F_E_esperada = 4.807e-16  # qE
        F_B_esperada = 6.409e-16  # q|v||B|
        
        error_E = abs(F_E.magnitude() - F_E_esperada) / F_E_esperada
        error_B = abs(F_B.magnitude() - F_B_esperada) / F_B_esperada
        
        print(f"\nVERIFICACIÓN:")
        print(f"Error F_E: {error_E:.2e}")
        print(f"Error F_B: {error_B:.2e}")
        
        if error_E < 0.01 and error_B < 0.01:
            print("✅ Ejemplo completo correcto")
            return True
        else:
            print("❌ Errores en el ejemplo")
            return False
        
    except Exception as e:
        print(f"❌ Error en ejemplo completo: {e}")
        return False

def main():
    """Ejecuta todas las pruebas de precisión"""
    print("🧪 SUITE DE PRUEBAS DE PRECISIÓN Y VALIDACIÓN")
    print("=" * 60)
    
    pruebas = [
        ("Precisión Numérica", test_precision_numerica),
        ("Validación de Unidades", test_validacion_unidades),
        ("Componentes Artificiales", test_componentes_artificiales),
        ("Producto Cruz", test_producto_cruz_precision),
        ("Radio de Curvatura", test_radio_curvatura),
        ("Ortogonalidad de Fuerzas", test_ortogonalidad_fuerzas),
        ("Ejemplo Completo", test_ejemplo_completo)
    ]
    
    exitosas = 0
    
    for nombre, prueba in pruebas:
        try:
            if prueba():
                exitosas += 1
                print(f"\n✅ {nombre}: EXITOSA")
            else:
                print(f"\n❌ {nombre}: FALLIDA")
        except Exception as e:
            print(f"\n❌ {nombre}: ERROR - {e}")
    
    print(f"\n" + "=" * 60)
    print(f"RESULTADO FINAL: {exitosas}/{len(pruebas)} pruebas exitosas")
    
    if exitosas == len(pruebas):
        print("\n🎉 ¡TODAS LAS MEJORAS FUNCIONAN CORRECTAMENTE!")
        print("✅ Precisión numérica mejorada")
        print("✅ Validación de unidades SI")
        print("✅ Sin componentes artificiales")
        print("✅ Cálculos vectoriales precisos")
        print("✅ Ortogonalidad verificada")
        print("\n🚀 El programa mejorado está listo para usar:")
        print("   python lorentz_mejorado.py")
    else:
        print(f"\n⚠️ {len(pruebas) - exitosas} pruebas fallaron")
        print("Revisa los errores antes de usar el programa mejorado")
    
    return exitosas == len(pruebas)

if __name__ == "__main__":
    main()
