#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CALCULADORA DE FUERZA DE LORENTZ - VERSIÓN FINAL
Programa completo y funcional con entrada de texto
Resuelve automáticamente problemas de física electromagnética
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox
import re
import math

class CalculadoraLorentzFinal:
    def __init__(self):
        # Constantes físicas
        self.e = 1.602176634e-19  # Carga elemental
        self.me = 9.1093837015e-31  # Masa electrón
        self.mp = 1.67262192369e-27  # Masa protón
        
        # Crear ventana
        self.ventana = tk.Tk()
        self.ventana.title("⚡ Calculadora de Fuerza de Lorentz ⚡")
        self.ventana.geometry("900x700")
        self.ventana.configure(bg='#f0f8ff')
        
        self.crear_interfaz()
    
    def crear_interfaz(self):
        # Título principal
        titulo = tk.Label(self.ventana, 
                         text="🔬 CALCULADORA DE FUERZA DE LORENTZ 🔬",
                         font=('Arial', 18, 'bold'), 
                         bg='#f0f8ff', fg='#000080')
        titulo.pack(pady=15)
        
        # Subtítulo
        subtitulo = tk.Label(self.ventana,
                           text="📝 Copia y pega tu problema de física aquí:",
                           font=('Arial', 12), bg='#f0f8ff', fg='#333333')
        subtitulo.pack(pady=5)
        
        # Marco para entrada
        marco_entrada = tk.Frame(self.ventana, bg='#f0f8ff')
        marco_entrada.pack(padx=20, pady=10, fill=tk.X)
        
        # Área de texto para entrada
        self.entrada = scrolledtext.ScrolledText(marco_entrada, 
                                               height=7, width=100,
                                               font=('Arial', 11), 
                                               wrap=tk.WORD,
                                               bg='white', fg='black')
        self.entrada.pack(fill=tk.X)
        
        # Ejemplo por defecto (el de tu imagen)
        ejemplo_imagen = """Un protón con una velocidad de v = (2 × 10⁵) î m/s se mueve en una región donde hay un campo eléctrico E = (3 × 10³) ĵ V/m y un campo magnético B = (0.02) k̂ T. Calcula la fuerza eléctrica, la fuerza magnética, la fuerza total que actúa sobre el protón, la aceleración resultante, determina el tipo de movimiento que describe la partícula y, en el caso de que se elimine el campo eléctrico, calcula el radio de la trayectoria circular que seguiría. Usa los valores q = 1.6 × 10⁻¹⁹ C y m = 1.67 × 10⁻²⁷ kg."""
        
        self.entrada.insert(tk.END, ejemplo_imagen)
        
        # Marco para botones
        marco_botones = tk.Frame(self.ventana, bg='#f0f8ff')
        marco_botones.pack(pady=15)
        
        # Botón resolver
        btn_resolver = tk.Button(marco_botones, 
                               text="🧮 RESOLVER PROBLEMA",
                               command=self.resolver_problema,
                               font=('Arial', 14, 'bold'),
                               bg='#32cd32', fg='white',
                               padx=25, pady=8,
                               relief=tk.RAISED, bd=3)
        btn_resolver.pack(side=tk.LEFT, padx=10)
        
        # Botón limpiar
        btn_limpiar = tk.Button(marco_botones,
                              text="🗑️ LIMPIAR",
                              command=self.limpiar_todo,
                              font=('Arial', 12),
                              bg='#ff6347', fg='white',
                              padx=20, pady=8,
                              relief=tk.RAISED, bd=3)
        btn_limpiar.pack(side=tk.LEFT, padx=10)
        
        # Botón ejemplo
        btn_ejemplo = tk.Button(marco_botones,
                              text="📚 NUEVO EJEMPLO",
                              command=self.cargar_ejemplo,
                              font=('Arial', 12),
                              bg='#4169e1', fg='white',
                              padx=20, pady=8,
                              relief=tk.RAISED, bd=3)
        btn_ejemplo.pack(side=tk.LEFT, padx=10)
        
        # Etiqueta para resultados
        etiqueta_resultados = tk.Label(self.ventana,
                                     text="📊 RESULTADOS Y SOLUCIÓN:",
                                     font=('Arial', 12, 'bold'),
                                     bg='#f0f8ff', fg='#000080')
        etiqueta_resultados.pack(anchor='w', padx=20, pady=(20,5))
        
        # Área de resultados
        marco_resultados = tk.Frame(self.ventana, bg='#f0f8ff')
        marco_resultados.pack(padx=20, pady=10, fill=tk.BOTH, expand=True)
        
        self.resultados = scrolledtext.ScrolledText(marco_resultados,
                                                  height=15, width=100,
                                                  font=('Courier', 10),
                                                  wrap=tk.WORD,
                                                  bg='#fffacd', fg='#000000')
        self.resultados.pack(fill=tk.BOTH, expand=True)
    
    def extraer_datos_del_texto(self, texto):
        """Extrae automáticamente los datos del problema"""
        # Convertir notación científica
        texto = re.sub(r'(\d+(?:\.\d+)?)\s*×\s*10\^?([+-]?\d+)', r'\1e\2', texto)
        texto = re.sub(r'(\d+(?:\.\d+)?)\s*\*\s*10\^?([+-]?\d+)', r'\1e\2', texto)
        
        datos = {}
        
        # Identificar tipo de partícula
        texto_lower = texto.lower()
        if 'protón' in texto_lower or 'proton' in texto_lower:
            datos['tipo'] = 'Protón'
            datos['q'] = self.e
            datos['m'] = self.mp
        elif 'electrón' in texto_lower or 'electron' in texto_lower:
            datos['tipo'] = 'Electrón'
            datos['q'] = -self.e
            datos['m'] = self.me
        else:
            datos['tipo'] = 'Partícula'
            datos['q'] = self.e
            datos['m'] = self.mp
        
        # Extraer vectores
        datos['v'] = self.extraer_vector_del_texto(texto, 'v')
        datos['E'] = self.extraer_vector_del_texto(texto, 'E')
        datos['B'] = self.extraer_vector_del_texto(texto, 'B')
        
        return datos
    
    def extraer_vector_del_texto(self, texto, variable):
        """Extrae un vector 3D del texto"""
        # Patrones para vectores unitarios î, ĵ, k̂
        patron_i = rf'{variable}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[îi]'
        patron_j = rf'{variable}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[ĵj]'
        patron_k = rf'{variable}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[k̂k]'
        
        x = self.buscar_numero_en_texto(texto, patron_i)
        y = self.buscar_numero_en_texto(texto, patron_j)
        z = self.buscar_numero_en_texto(texto, patron_k)
        
        # Si no encuentra vectores unitarios, buscar formato (x, y, z)
        if x == 0 and y == 0 and z == 0:
            patron_parentesis = rf'{variable}[^=]*=\s*\(([^)]+)\)'
            match = re.search(patron_parentesis, texto, re.IGNORECASE)
            if match:
                componentes = match.group(1).split(',')
                if len(componentes) >= 3:
                    try:
                        x = float(re.sub(r'[^\d.e+-]', '', componentes[0]))
                        y = float(re.sub(r'[^\d.e+-]', '', componentes[1]))
                        z = float(re.sub(r'[^\d.e+-]', '', componentes[2]))
                    except:
                        pass
        
        return [x, y, z]
    
    def buscar_numero_en_texto(self, texto, patron):
        """Busca y extrae un número usando un patrón regex"""
        match = re.search(patron, texto, re.IGNORECASE)
        if match:
            try:
                return float(match.group(1))
            except:
                return 0
        return 0
    
    def calcular_magnitud(self, vector):
        """Calcula la magnitud de un vector"""
        return math.sqrt(vector[0]**2 + vector[1]**2 + vector[2]**2)
    
    def producto_cruz(self, a, b):
        """Calcula el producto cruz a × b"""
        return [
            a[1]*b[2] - a[2]*b[1],
            a[2]*b[0] - a[0]*b[2],
            a[0]*b[1] - a[1]*b[0]
        ]
    
    def resolver_problema(self):
        """Resuelve el problema completo"""
        texto = self.entrada.get(1.0, tk.END).strip()
        
        if not texto:
            messagebox.showwarning("⚠️ Advertencia", "Por favor ingresa un problema para resolver")
            return
        
        try:
            # Extraer datos automáticamente
            datos = self.extraer_datos_del_texto(texto)
            
            # Realizar todos los cálculos
            self.calcular_y_mostrar_solucion(datos)
            
        except Exception as e:
            messagebox.showerror("❌ Error", f"Error al resolver el problema:\n{e}")
    
    def calcular_y_mostrar_solucion(self, datos):
        """Calcula todas las magnitudes y muestra la solución completa"""
        self.resultados.delete(1.0, tk.END)
        
        # Extraer variables
        q, m = datos['q'], datos['m']
        v, E, B = datos['v'], datos['E'], datos['B']
        
        # Crear el texto de solución
        solucion = "=" * 70 + "\n"
        solucion += "🔬 SOLUCIÓN COMPLETA DEL PROBLEMA DE FUERZA DE LORENTZ\n"
        solucion += "=" * 70 + "\n\n"
        
        # Mostrar datos extraídos
        solucion += "📋 DATOS IDENTIFICADOS AUTOMÁTICAMENTE:\n"
        solucion += f"   🔸 Partícula: {datos['tipo']}\n"
        solucion += f"   🔸 Carga (q): {q:.3e} C\n"
        solucion += f"   🔸 Masa (m): {m:.3e} kg\n"
        solucion += f"   🔸 Velocidad (v): ({v[0]:.0f}, {v[1]:.0f}, {v[2]:.0f}) m/s\n"
        solucion += f"   🔸 Campo eléctrico (E): ({E[0]:.0f}, {E[1]:.0f}, {E[2]:.0f}) V/m\n"
        solucion += f"   🔸 Campo magnético (B): ({B[0]:.3f}, {B[1]:.3f}, {B[2]:.3f}) T\n\n"
        
        # CÁLCULO 1: Fuerza eléctrica
        F_E = [q * E[0], q * E[1], q * E[2]]
        mag_F_E = self.calcular_magnitud(F_E)
        
        solucion += "⚡ CÁLCULO DE FUERZAS:\n\n"
        solucion += "1️⃣ FUERZA ELÉCTRICA (F_E = qE):\n"
        solucion += f"   F_E = ({F_E[0]:.3e}, {F_E[1]:.3e}, {F_E[2]:.3e}) N\n"
        solucion += f"   |F_E| = {mag_F_E:.3e} N\n\n"
        
        # CÁLCULO 2: Fuerza magnética
        vxB = self.producto_cruz(v, B)
        F_B = [q * vxB[0], q * vxB[1], q * vxB[2]]
        mag_F_B = self.calcular_magnitud(F_B)
        
        solucion += "2️⃣ FUERZA MAGNÉTICA (F_B = q(v × B)):\n"
        solucion += f"   v × B = ({vxB[0]:.3e}, {vxB[1]:.3e}, {vxB[2]:.3e})\n"
        solucion += f"   F_B = ({F_B[0]:.3e}, {F_B[1]:.3e}, {F_B[2]:.3e}) N\n"
        solucion += f"   |F_B| = {mag_F_B:.3e} N\n\n"
        
        # CÁLCULO 3: Fuerza total
        F_total = [F_E[0] + F_B[0], F_E[1] + F_B[1], F_E[2] + F_B[2]]
        mag_F_total = self.calcular_magnitud(F_total)
        
        solucion += "3️⃣ FUERZA TOTAL (F = F_E + F_B):\n"
        solucion += f"   F_total = ({F_total[0]:.3e}, {F_total[1]:.3e}, {F_total[2]:.3e}) N\n"
        solucion += f"   |F_total| = {mag_F_total:.3e} N\n\n"
        
        # CÁLCULO 4: Aceleración
        a = [F_total[0]/m, F_total[1]/m, F_total[2]/m]
        mag_a = self.calcular_magnitud(a)
        
        solucion += "🚀 ACELERACIÓN RESULTANTE:\n"
        solucion += f"   a = F/m = ({a[0]:.3e}, {a[1]:.3e}, {a[2]:.3e}) m/s²\n"
        solucion += f"   |a| = {mag_a:.3e} m/s²\n\n"
        
        # CÁLCULO 5: Tipo de movimiento
        mag_E = self.calcular_magnitud(E)
        mag_B = self.calcular_magnitud(B)
        
        solucion += "🔄 ANÁLISIS DEL TIPO DE MOVIMIENTO:\n"
        if mag_E > 0 and mag_B > 0:
            solucion += "   🔸 Campos eléctrico y magnético simultáneos\n"
            solucion += "   🔸 Movimiento complejo con deriva E×B\n"
            solucion += "   🔸 Trayectoria cicloidal o trocoidal\n\n"
        elif mag_E > 0:
            solucion += "   🔸 Solo campo eléctrico presente\n"
            solucion += "   🔸 Movimiento parabólico o rectilíneo acelerado\n\n"
        elif mag_B > 0:
            solucion += "   🔸 Solo campo magnético presente\n"
            solucion += "   🔸 Movimiento circular o helicoidal\n\n"
        
        # CÁLCULO 6: Radio sin campo eléctrico
        if mag_B > 0:
            mag_v = self.calcular_magnitud(v)
            radio = (m * mag_v) / (abs(q) * mag_B)
            periodo = (2 * math.pi * m) / (abs(q) * mag_B)
            frecuencia = 1 / periodo
            
            solucion += "🔵 ANÁLISIS SIN CAMPO ELÉCTRICO:\n"
            solucion += f"   Radio de trayectoria circular: r = mv/(qB)\n"
            solucion += f"   r = {radio:.3e} m = {radio*100:.2f} cm\n"
            solucion += f"   Período ciclotrónico: T = {periodo:.3e} s\n"
            solucion += f"   Frecuencia: f = {frecuencia:.3e} Hz\n\n"
        
        # CÁLCULO 7: Energía
        mag_v = self.calcular_magnitud(v)
        energia_cinetica = 0.5 * m * mag_v**2
        
        solucion += "⚡ ANÁLISIS ENERGÉTICO:\n"
        solucion += f"   Energía cinética: K = ½mv² = {energia_cinetica:.3e} J\n"
        solucion += f"   En electronvoltios: K = {energia_cinetica/self.e:.3e} eV\n\n"
        
        # Resumen final
        solucion += "✅ RESUMEN DE RESULTADOS:\n"
        solucion += f"   • Fuerza eléctrica: {mag_F_E:.2e} N\n"
        solucion += f"   • Fuerza magnética: {mag_F_B:.2e} N\n"
        solucion += f"   • Fuerza total: {mag_F_total:.2e} N\n"
        solucion += f"   • Aceleración: {mag_a:.2e} m/s²\n"
        if mag_B > 0:
            solucion += f"   • Radio circular (sin E): {radio:.2e} m\n"
        
        solucion += "\n🎉 ¡PROBLEMA RESUELTO COMPLETAMENTE!"
        
        # Mostrar la solución
        self.resultados.insert(tk.END, solucion)
    
    def limpiar_todo(self):
        """Limpia todas las áreas de texto"""
        self.entrada.delete(1.0, tk.END)
        self.resultados.delete(1.0, tk.END)
    
    def cargar_ejemplo(self):
        """Carga un ejemplo diferente"""
        ejemplos = [
            "Un electrón con velocidad v = 1e6 m/s en dirección x entra en un campo magnético B = 0.1 T en dirección z. Calcula el radio de la órbita circular y el período.",
            
            "Una partícula con carga q = 1.6e-19 C y masa m = 1.67e-27 kg tiene velocidad v = (1e5, 1e5, 0) m/s en un campo magnético B = (0, 0, 0.5) T. Analiza el movimiento helicoidal.",
            
            "Un protón se acelera desde el reposo por un campo eléctrico E = 1000 V/m. Luego entra en un campo magnético B = 0.02 T perpendicular a su velocidad. Calcula todas las fuerzas.",
            
            "Electrón con velocidad v = (5e5) î m/s en campos cruzados E = (0, 2000, 0) V/m y B = (0, 0, 0.01) T. Determina si hay deriva E×B."
        ]
        
        import random
        ejemplo_seleccionado = random.choice(ejemplos)
        self.entrada.delete(1.0, tk.END)
        self.entrada.insert(tk.END, ejemplo_seleccionado)
    
    def ejecutar(self):
        """Ejecuta la aplicación"""
        self.ventana.mainloop()

def main():
    """Función principal"""
    try:
        print("🚀 Iniciando Calculadora de Fuerza de Lorentz...")
        app = CalculadoraLorentzFinal()
        app.ejecutar()
    except Exception as e:
        print(f"❌ Error al ejecutar: {e}")
        input("Presiona Enter para salir...")

if __name__ == "__main__":
    main()
