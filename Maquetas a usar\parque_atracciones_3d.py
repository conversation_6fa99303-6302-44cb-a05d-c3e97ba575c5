import bpy
import math
import random

# Limpiar escena
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete()

# Configurar renderizado
bpy.context.scene.render.engine = 'CYCLES'
bpy.context.scene.cycles.device = 'GPU'
bpy.context.scene.cycles.samples = 128

# Configurar viewport para mejor calidad
bpy.context.scene.cycles.preview_samples = 32  # Muestras para viewport
bpy.context.scene.render.resolution_x = 1920
bpy.context.scene.render.resolution_y = 1080
bpy.context.scene.render.resolution_percentage = 100

# Configurar viewport shading para mejor visualización
for area in bpy.context.screen.areas:
    if area.type == 'VIEW_3D':
        for space in area.spaces:
            if space.type == 'VIEW_3D':
                space.shading.type = 'MATERIAL'  # Cambiar a Material Preview
                space.shading.use_scene_lights = True
                space.shading.use_scene_world = True
                space.shading.studio_light = 'forest.exr'  # Iluminación HDRI
                break

# Crear suelo del parque
bpy.ops.mesh.primitive_plane_add(size=50, enter_editmode=False, align='WORLD', location=(0, 0, 0))
suelo = bpy.context.active_object
suelo.name = "Suelo_Parque"

# Añadir material al suelo (césped con textura)
mat_cesped = bpy.data.materials.new(name="Cesped")
mat_cesped.use_nodes = True
nodes = mat_cesped.node_tree.nodes
links = mat_cesped.node_tree.links

# Limpiar nodos existentes
for node in nodes:
    nodes.remove(node)

# Crear nodos
output = nodes.new(type='ShaderNodeOutputMaterial')
principled = nodes.new(type='ShaderNodeBsdfPrincipled')
tex_coord = nodes.new(type='ShaderNodeTexCoord')
mapping = nodes.new(type='ShaderNodeMapping')
noise_texture = nodes.new(type='ShaderNodeTexNoise')
color_ramp = nodes.new(type='ShaderNodeValToRGB')
bump = nodes.new(type='ShaderNodeBump')

# Configurar nodos
noise_texture.inputs['Scale'].default_value = 20.0
noise_texture.inputs['Detail'].default_value = 10.0

# Configurar ColorRamp para variaciones de verde
color_ramp.color_ramp.elements[0].position = 0.3
color_ramp.color_ramp.elements[0].color = (0.05, 0.3, 0.05, 1.0)  # Verde oscuro
color_ramp.color_ramp.elements[1].position = 0.7
color_ramp.color_ramp.elements[1].color = (0.2, 0.5, 0.1, 1.0)    # Verde claro

# Añadir un punto intermedio al ColorRamp
color_ramp.color_ramp.elements.new(0.5)
color_ramp.color_ramp.elements[1].color = (0.1, 0.4, 0.1, 1.0)    # Verde medio

# Configurar Principled BSDF
principled.inputs['Roughness'].default_value = 0.9
principled.inputs['Specular'].default_value = 0.1

# Configurar Bump
bump.inputs['Strength'].default_value = 0.2

# Conectar nodos
links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
links.new(mapping.outputs['Vector'], noise_texture.inputs['Vector'])
links.new(noise_texture.outputs['Fac'], color_ramp.inputs['Fac'])
links.new(noise_texture.outputs['Fac'], bump.inputs['Height'])
links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
links.new(bump.outputs['Normal'], principled.inputs['Normal'])
links.new(principled.outputs['BSDF'], output.inputs['Surface'])

suelo.data.materials.append(mat_cesped)

# Función para crear una noria
def crear_noria(location):
    # Centro de la noria
    bpy.ops.mesh.primitive_cylinder_add(radius=1, depth=0.5, enter_editmode=False, location=(location[0], location[1], 1))
    centro = bpy.context.active_object
    centro.name = "Centro_Noria"

    # Estructura principal
    bpy.ops.mesh.primitive_cylinder_add(radius=8, depth=0.3, enter_editmode=False, location=(location[0], location[1], 1))
    estructura = bpy.context.active_object
    estructura.name = "Estructura_Noria"

    # Crear cabinas
    num_cabinas = 12
    radio = 7.5
    for i in range(num_cabinas):
        angulo = (2 * math.pi / num_cabinas) * i
        x = location[0] + radio * math.cos(angulo)
        y = location[1] + radio * math.sin(angulo)
        z = 1 + radio * 0.1  # Ligera elevación

        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(x, y, z))
        cabina = bpy.context.active_object
        cabina.name = f"Cabina_{i}"

        # Material para cabinas
        mat_cabina = bpy.data.materials.new(name=f"Material_Cabina_{i}")
        mat_cabina.use_nodes = True
        nodes = mat_cabina.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        # Color aleatorio para cada cabina
        r, g, b = random.random(), random.random(), random.random()
        bsdf.inputs[0].default_value = (r, g, b, 1.0)
        cabina.data.materials.append(mat_cabina)

        # Conectar cabina al centro con "radios"
        bpy.ops.mesh.primitive_cylinder_add(radius=0.1, depth=radio, enter_editmode=False)
        radio_obj = bpy.context.active_object
        radio_obj.name = f"Radio_{i}"

        # Rotar y posicionar el radio
        radio_obj.rotation_euler[0] = math.pi/2
        radio_obj.rotation_euler[2] = angulo
        radio_obj.location = (location[0], location[1], 1)

# Función para crear una montaña rusa
def crear_montana_rusa(location):
    # Crear puntos de control para la pista
    puntos = []
    altura_max = 10
    longitud = 20
    ancho = 15

    # Generar puntos para una pista con subidas y bajadas
    num_puntos = 30
    for i in range(num_puntos):
        t = i / (num_puntos - 1)
        x = location[0] - longitud/2 + longitud * t
        y = location[1] + ancho/2 * math.sin(t * 4 * math.pi)

        # Altura con subidas y bajadas
        if i < num_puntos / 4:
            z = altura_max * (4 * i / num_puntos)  # Subida inicial
        elif i < num_puntos / 2:
            z = altura_max * (1 - 0.5 * (4 * (i - num_puntos/4) / num_puntos))  # Primera bajada
        elif i < 3 * num_puntos / 4:
            z = altura_max * 0.5 + altura_max * 0.3 * math.sin(t * 8 * math.pi)  # Ondulaciones
        else:
            z = altura_max * 0.5 * (1 - (4 * (i - 3*num_puntos/4) / num_puntos))  # Bajada final

        puntos.append((x, y, z))

    # Crear curva a partir de los puntos
    curva = bpy.data.curves.new('PistaMontanaRusa', 'CURVE')
    curva.dimensions = '3D'

    spline = curva.splines.new('NURBS')
    spline.points.add(len(puntos) - 1)

    for i, punto in enumerate(puntos):
        spline.points[i].co = (punto[0], punto[1], punto[2], 1)

    # Crear objeto a partir de la curva
    obj_curva = bpy.data.objects.new('MontanaRusa', curva)
    bpy.context.collection.objects.link(obj_curva)

    # Dar volumen a la curva
    curva.bevel_depth = 0.2
    curva.bevel_resolution = 4

    # Material para la montaña rusa
    mat_montana = bpy.data.materials.new(name="Material_MontanaRusa")
    mat_montana.use_nodes = True
    nodes = mat_montana.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.8, 0.1, 0.1, 1.0)  # Rojo
    bsdf.inputs[7].default_value = 0.2  # Rugosidad
    obj_curva.data.materials.append(mat_montana)

    # Crear soportes
    for i in range(0, len(puntos), 3):
        if puntos[i][2] > 0.5:  # Solo crear soportes si hay altura
            bpy.ops.mesh.primitive_cylinder_add(radius=0.3, depth=puntos[i][2],
                                              enter_editmode=False,
                                              location=(puntos[i][0], puntos[i][1], puntos[i][2]/2))
            soporte = bpy.context.active_object
            soporte.name = f"Soporte_{i}"

            # Material para soportes
            mat_soporte = bpy.data.materials.new(name=f"Material_Soporte_{i}")
            mat_soporte.use_nodes = True
            nodes = mat_soporte.node_tree.nodes
            bsdf = nodes.get("Principled BSDF")
            bsdf.inputs[0].default_value = (0.2, 0.2, 0.2, 1.0)  # Gris
            soporte.data.materials.append(mat_soporte)

# Función para crear un carrusel
def crear_carrusel(location):
    # Base del carrusel
    bpy.ops.mesh.primitive_cylinder_add(radius=5, depth=0.5, enter_editmode=False, location=(location[0], location[1], 0.25))
    base = bpy.context.active_object
    base.name = "Base_Carrusel"

    # Poste central
    bpy.ops.mesh.primitive_cylinder_add(radius=0.5, depth=5, enter_editmode=False, location=(location[0], location[1], 2.75))
    poste = bpy.context.active_object
    poste.name = "Poste_Carrusel"

    # Techo del carrusel
    bpy.ops.mesh.primitive_cone_add(radius1=5.5, radius2=0, depth=2, enter_editmode=False, location=(location[0], location[1], 6))
    techo = bpy.context.active_object
    techo.name = "Techo_Carrusel"

    # Crear caballos u otros elementos
    num_elementos = 8
    radio = 4
    for i in range(num_elementos):
        angulo = (2 * math.pi / num_elementos) * i
        x = location[0] + radio * math.cos(angulo)
        y = location[1] + radio * math.sin(angulo)

        # Crear un caballo simplificado (usando un cubo deformado)
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(x, y, 2))
        caballo = bpy.context.active_object
        caballo.name = f"Caballo_{i}"
        caballo.scale = (0.8, 1.5, 1.2)

        # Material para caballos
        mat_caballo = bpy.data.materials.new(name=f"Material_Caballo_{i}")
        mat_caballo.use_nodes = True
        nodes = mat_caballo.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        # Colores alternados
        if i % 2 == 0:
            bsdf.inputs[0].default_value = (0.8, 0.1, 0.1, 1.0)  # Rojo
        else:
            bsdf.inputs[0].default_value = (1.0, 1.0, 1.0, 1.0)  # Blanco
        caballo.data.materials.append(mat_caballo)

        # Poste para el caballo
        bpy.ops.mesh.primitive_cylinder_add(radius=0.1, depth=2, enter_editmode=False, location=(x, y, 1))
        poste_caballo = bpy.context.active_object
        poste_caballo.name = f"Poste_Caballo_{i}"

# Crear atracciones
crear_noria((-15, 15, 0))
crear_montana_rusa((0, -10, 0))
crear_carrusel((15, 10, 0))

# Añadir árboles decorativos
for _ in range(20):
    x = random.uniform(-25, 25)
    y = random.uniform(-25, 25)

    # Evitar colocar árboles donde hay atracciones
    if ((x+15)**2 + (y-15)**2 > 100 and  # Lejos de la noria
        (x-15)**2 + (y-10)**2 > 36 and   # Lejos del carrusel
        abs(x) > 5 or abs(y+10) > 5):    # Lejos de la montaña rusa

        # Tronco
        bpy.ops.mesh.primitive_cylinder_add(radius=0.3, depth=random.uniform(2, 4),
                                          enter_editmode=False, location=(x, y, 1))
        tronco = bpy.context.active_object
        tronco.name = f"Tronco_Arbol_{x}_{y}"

        # Material para tronco
        mat_tronco = bpy.data.materials.new(name=f"Material_Tronco_{x}_{y}")
        mat_tronco.use_nodes = True
        nodes = mat_tronco.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        bsdf.inputs[0].default_value = (0.3, 0.2, 0.1, 1.0)  # Marrón
        tronco.data.materials.append(mat_tronco)

        # Copa del árbol
        altura_tronco = tronco.dimensions.z
        bpy.ops.mesh.primitive_ico_sphere_add(radius=random.uniform(1, 1.5),
                                            enter_editmode=False,
                                            location=(x, y, altura_tronco + 1))
        copa = bpy.context.active_object
        copa.name = f"Copa_Arbol_{x}_{y}"

        # Material para copa
        mat_copa = bpy.data.materials.new(name=f"Material_Copa_{x}_{y}")
        mat_copa.use_nodes = True
        nodes = mat_copa.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        verde = random.uniform(0.2, 0.4)
        bsdf.inputs[0].default_value = (0.1, verde, 0.1, 1.0)  # Verde variado
        copa.data.materials.append(mat_copa)

# Añadir caminos
def crear_camino(inicio, fin, ancho=1.5):
    # Calcular dirección y longitud
    direccion = (fin[0] - inicio[0], fin[1] - inicio[1])
    longitud = math.sqrt(direccion[0]**2 + direccion[1]**2)

    # Crear plano para el camino
    bpy.ops.mesh.primitive_plane_add(size=1, enter_editmode=False, location=(
        inicio[0] + direccion[0]/2,
        inicio[1] + direccion[1]/2,
        0.01))  # Ligeramente por encima del suelo
    camino = bpy.context.active_object
    camino.name = f"Camino_{inicio}_{fin}"

    # Escalar y rotar el camino
    camino.scale.x = longitud / 2
    camino.scale.y = ancho / 2

    # Rotar para alinear con la dirección
    angulo = math.atan2(direccion[1], direccion[0])
    camino.rotation_euler.z = angulo

    # Material para camino
    mat_camino = bpy.data.materials.new(name=f"Material_Camino_{inicio}_{fin}")
    mat_camino.use_nodes = True
    nodes = mat_camino.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.3, 0.3, 0.3, 1.0)  # Gris
    camino.data.materials.append(mat_camino)

# Crear caminos entre atracciones
crear_camino((-15, 15), (0, -10))  # Noria a Montaña Rusa
crear_camino((0, -10), (15, 10))   # Montaña Rusa a Carrusel
crear_camino((15, 10), (-15, 15))  # Carrusel a Noria

# Añadir entrada al parque
bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False, location=(0, 25, 1.5))
entrada = bpy.context.active_object
entrada.name = "Entrada_Parque"
entrada.scale = (8, 1, 3)

# Crear arco en la entrada
bpy.ops.mesh.primitive_cylinder_add(radius=4, depth=1, enter_editmode=False, location=(0, 25, 4))
arco = bpy.context.active_object
arco.name = "Arco_Entrada"
arco.rotation_euler.x = math.pi/2
arco.scale = (1, 1, 0.2)

# Material para entrada
mat_entrada = bpy.data.materials.new(name="Material_Entrada")
mat_entrada.use_nodes = True
nodes = mat_entrada.node_tree.nodes
bsdf = nodes.get("Principled BSDF")
bsdf.inputs[0].default_value = (0.7, 0.3, 0.3, 1.0)  # Rojo ladrillo
entrada.data.materials.append(mat_entrada)
arco.data.materials.append(mat_entrada)

# Crear camino desde la entrada
crear_camino((0, 25), (0, 15), ancho=3)
crear_camino((0, 15), (-15, 15), ancho=2)

# Añadir iluminación mejorada
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 50))
sol = bpy.context.active_object
sol.name = "Sol"
sol.data.energy = 5
sol.rotation_euler = (math.radians(60), 0, math.radians(30))

# Añadir luz de relleno para mejor iluminación
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 50))
luz_relleno = bpy.context.active_object
luz_relleno.name = "Luz_Relleno"
luz_relleno.data.energy = 2
luz_relleno.rotation_euler = (math.radians(60), 0, math.radians(210))

# Añadir luz ambiental
bpy.ops.object.light_add(type='AREA', radius=1, location=(0, 0, 30))
luz_ambiental = bpy.context.active_object
luz_ambiental.name = "Luz_Ambiental"
luz_ambiental.data.energy = 50
luz_ambiental.data.size = 50

# Añadir cámara
bpy.ops.object.camera_add(enter_editmode=False, align='VIEW', location=(30, -30, 25), rotation=(math.radians(60), 0, math.radians(45)))
camara = bpy.context.active_object
camara.name = "Camara_Parque"
bpy.context.scene.camera = camara

# Configurar fondo de cielo
world = bpy.context.scene.world
world.use_nodes = True
bg = world.node_tree.nodes['Background']
bg.inputs[0].default_value = (0.5, 0.7, 1.0, 1.0)  # Azul cielo
bg.inputs[1].default_value = 1.0  # Intensidad

# Función para añadir animación al carrusel
def animar_carrusel():
    # Buscar todos los objetos del carrusel
    objetos_carrusel = []

    # Encontrar la base del carrusel
    base_carrusel = None
    for obj in bpy.data.objects:
        if "Base_Carrusel" in obj.name:
            base_carrusel = obj
            break

    if base_carrusel:
        # Configurar animación de rotación
        base_carrusel.rotation_euler = (0, 0, 0)
        base_carrusel.keyframe_insert(data_path="rotation_euler", frame=1)

        # Configurar la rotación final (una vuelta completa en 120 frames)
        base_carrusel.rotation_euler = (0, 0, math.pi * 2)
        base_carrusel.keyframe_insert(data_path="rotation_euler", frame=120)

        # Configurar interpolación lineal para rotación suave
        if base_carrusel.animation_data and base_carrusel.animation_data.action:
            for fcurve in base_carrusel.animation_data.action.fcurves:
                for keyframe in fcurve.keyframe_points:
                    keyframe.interpolation = 'LINEAR'

    # Animar también los caballos y postes
    for obj in bpy.data.objects:
        if "Caballo_" in obj.name or "Poste_Caballo_" in obj.name:
            # Configurar animación de rotación alrededor del centro del carrusel
            obj.rotation_euler = (0, 0, 0)
            obj.keyframe_insert(data_path="rotation_euler", frame=1)

            obj.rotation_euler = (0, 0, math.pi * 2)
            obj.keyframe_insert(data_path="rotation_euler", frame=120)

            # Configurar interpolación lineal
            if obj.animation_data and obj.animation_data.action:
                for fcurve in obj.animation_data.action.fcurves:
                    for keyframe in fcurve.keyframe_points:
                        keyframe.interpolation = 'LINEAR'

    # Configurar el techo del carrusel para que también gire
    for obj in bpy.data.objects:
        if "Techo_Carrusel" in obj.name or "Poste_Carrusel" in obj.name:
            obj.rotation_euler = (0, 0, 0)
            obj.keyframe_insert(data_path="rotation_euler", frame=1)

            obj.rotation_euler = (0, 0, math.pi * 2)
            obj.keyframe_insert(data_path="rotation_euler", frame=120)

            if obj.animation_data and obj.animation_data.action:
                for fcurve in obj.animation_data.action.fcurves:
                    for keyframe in fcurve.keyframe_points:
                        keyframe.interpolation = 'LINEAR'

# Función para añadir animación a la noria
def animar_noria():
    # Buscar todos los objetos de la noria
    for obj in bpy.data.objects:
        if ("Estructura_Noria" in obj.name or "Cabina_" in obj.name or
            "Radio_" in obj.name):
            # Configurar animación de rotación más lenta para la noria
            obj.rotation_euler = (0, 0, 0)
            obj.keyframe_insert(data_path="rotation_euler", frame=1)

            obj.rotation_euler = (0, 0, math.pi * 2)
            obj.keyframe_insert(data_path="rotation_euler", frame=200)  # Más lenta que el carrusel

            if obj.animation_data and obj.animation_data.action:
                for fcurve in obj.animation_data.action.fcurves:
                    for keyframe in fcurve.keyframe_points:
                        keyframe.interpolation = 'LINEAR'

# Aplicar animaciones a las atracciones
animar_carrusel()
animar_noria()

# Configurar la línea de tiempo para la animación
bpy.context.scene.frame_start = 1
bpy.context.scene.frame_end = 200
bpy.context.scene.frame_current = 1

# Configurar reproducción en bucle infinito
for area in bpy.context.screen.areas:
    if area.type == 'TIMELINE':
        for region in area.regions:
            if region.type == 'WINDOW':
                with bpy.context.temp_override(area=area, region=region):
                    bpy.ops.screen.animation_play()
                break
        break

print("¡Maqueta del parque de atracciones creada con éxito!")
print("¡Animación del carrusel y noria activada!")
print("Para renderizar, presiona F12 o usa el menú Render > Render Image")
print("Para ver la animación, presiona la barra espaciadora o el botón de play en la timeline")
