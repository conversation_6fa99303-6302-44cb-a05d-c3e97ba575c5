"""
Script de inicio para la Calculadora de Fuerza de Lorentz
"""

import sys
import os
import subprocess

def verificar_dependencias():
    """Verifica que las dependencias estén instaladas"""
    try:
        import numpy
        import matplotlib
        import tkinter
        return True
    except ImportError:
        return False

def instalar_dependencias():
    """Instala las dependencias necesarias"""
    print("Instalando dependencias...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "numpy", "matplotlib"])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Función principal"""
    print("=" * 60)
    print("CALCULADORA DE FUERZA DE LORENTZ")
    print("Programa educativo de física electromagnética")
    print("=" * 60)

    # Verificar dependencias
    if not verificar_dependencias():
        print("\n⚠ Dependencias faltantes detectadas.")
        respuesta = input("¿Deseas instalarlas automáticamente? (s/n): ")

        if respuesta.lower() in ['s', 'si', 'sí', 'y', 'yes']:
            if instalar_dependencias():
                print("✓ Dependencias instaladas correctamente")
            else:
                print("❌ Error al instalar dependencias")
                print("Instala manualmente con: pip install numpy matplotlib")
                return
        else:
            print("Instala las dependencias manualmente y vuelve a ejecutar")
            return

    # Mostrar opciones de interfaz
    print("\n🎯 OPCIONES DISPONIBLES:")
    print("1. 📝 Interfaz con entrada de TEXTO (¡NUEVO!)")
    print("   - Copia y pega problemas de física")
    print("   - Reconocimiento automático de datos")
    print("   - Ideal para ejercicios de libros/exámenes")
    print()
    print("2. 🖥️ Interfaz gráfica TRADICIONAL")
    print("   - Entrada manual de datos")
    print("   - Control total de parámetros")
    print("   - Herramientas avanzadas")
    print()
    print("3. 🧪 Demo SIMPLE (sin interfaz gráfica)")
    print("   - Ejemplos básicos en consola")
    print("   - Verificación rápida")

    while True:
        try:
            opcion = input("\n¿Qué interfaz prefieres? (1/2/3): ").strip()

            if opcion == "1":
                print("\n🚀 Iniciando interfaz con entrada de texto...")
                ejecutar_interfaz_texto()
                break
            elif opcion == "2":
                print("\n🚀 Iniciando interfaz gráfica tradicional...")
                ejecutar_interfaz_tradicional()
                break
            elif opcion == "3":
                print("\n🚀 Ejecutando demo simple...")
                ejecutar_demo_simple()
                break
            else:
                print("❌ Opción inválida. Elige 1, 2 o 3.")

        except KeyboardInterrupt:
            print("\n\n👋 ¡Hasta luego!")
            return

def ejecutar_interfaz_texto():
    """Ejecuta la interfaz con entrada de texto"""
    try:
        from interfaz_texto import main as run_text_interface
        run_text_interface()
    except ImportError as e:
        print(f"❌ Error al importar interfaz de texto: {e}")
        print("Verifica que todos los archivos estén en la misma carpeta")
    except Exception as e:
        print(f"❌ Error inesperado: {e}")

def ejecutar_interfaz_tradicional():
    """Ejecuta la interfaz gráfica tradicional"""
    try:
        from fuerza_lorentz_main import main as run_main
        run_main()
    except ImportError as e:
        print(f"❌ Error al importar interfaz tradicional: {e}")
        print("Verifica que todos los archivos estén en la misma carpeta")
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        print("\nPuedes probar ejecutar directamente:")
        print("python fuerza_lorentz_main.py")

def ejecutar_demo_simple():
    """Ejecuta la demo simple"""
    try:
        import demo_simple
        # El demo se ejecuta automáticamente al importar
    except ImportError as e:
        print(f"❌ Error al importar demo: {e}")
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        print("\nPuedes probar ejecutar directamente:")
        print("python demo_simple.py")

if __name__ == "__main__":
    main()
