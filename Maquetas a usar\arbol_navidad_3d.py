import bpy
import math
import random

# Limpiar escena
if bpy.context.mode != 'OBJECT':
    bpy.ops.object.mode_set(mode='OBJECT')

# Se<PERSON>ccionar todos los objetos
for obj in bpy.context.scene.objects:
    obj.select_set(True)

# Eliminar todos los objetos
bpy.ops.object.delete(use_global=False)

# Función para crear el tronco del árbol
def crear_tronco():
    bpy.ops.mesh.primitive_cylinder_add(radius=0.3, depth=2, enter_editmode=False, location=(0, 0, 1))
    tronco = bpy.context.active_object
    tronco.name = "Tronco_Arbol"
    
    # Material para tronco
    mat_tronco = bpy.data.materials.new(name="Material_Tronco")
    mat_tronco.use_nodes = True
    nodes = mat_tronco.node_tree.nodes
    links = mat_tronco.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos para tronco realista
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    noise = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    
    # Configurar propiedades del tronco
    principled.inputs['Base Color'].default_value = (0.3, 0.2, 0.1, 1.0)  # Marrón
    principled.inputs['Roughness'].default_value = 0.9
    principled.inputs['Specular'].default_value = 0.1
    
    # Configurar ruido para textura de corteza
    noise.inputs['Scale'].default_value = 20.0
    noise.inputs['Detail'].default_value = 10.0
    
    # ColorRamp para variaciones de marrón
    color_ramp.color_ramp.elements[0].color = (0.2, 0.1, 0.05, 1.0)  # Marrón oscuro
    color_ramp.color_ramp.elements[1].color = (0.4, 0.25, 0.15, 1.0)  # Marrón claro
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise.inputs['Vector'])
    links.new(noise.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Aplicar material
    tronco.data.materials.append(mat_tronco)
    
    return tronco

# Función para crear las capas del árbol
def crear_capas_arbol():
    capas = []
    alturas = [3, 4.5, 6, 7.5]  # Diferentes alturas para las capas
    radios = [3.5, 2.8, 2.1, 1.4]  # Radios decrecientes
    
    for i, (altura, radio) in enumerate(zip(alturas, radios)):
        # Crear cono para cada capa
        bpy.ops.mesh.primitive_cone_add(radius1=radio, radius2=0.2, depth=2,
                                      enter_editmode=False, 
                                      location=(0, 0, altura))
        capa = bpy.context.active_object
        capa.name = f"Capa_Arbol_{i}"
        
        # Hacer la forma más irregular y natural
        bpy.context.view_layer.objects.active = capa
        bpy.ops.object.mode_set(mode='EDIT')
        bpy.ops.mesh.subdivide(number_cuts=2)
        bpy.ops.transform.resize(value=(random.uniform(0.9, 1.1), 
                                      random.uniform(0.9, 1.1), 
                                      1.0))
        bpy.ops.object.mode_set(mode='OBJECT')
        
        capas.append(capa)
    
    # Material para las ramas/hojas
    mat_hojas = bpy.data.materials.new(name="Material_Hojas")
    mat_hojas.use_nodes = True
    nodes = mat_hojas.node_tree.nodes
    links = mat_hojas.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos para hojas verdes
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    noise = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    
    # Configurar propiedades de las hojas
    principled.inputs['Base Color'].default_value = (0.1, 0.4, 0.1, 1.0)  # Verde oscuro
    principled.inputs['Roughness'].default_value = 0.8
    principled.inputs['Specular'].default_value = 0.2
    
    # Configurar ruido para variación
    noise.inputs['Scale'].default_value = 15.0
    noise.inputs['Detail'].default_value = 8.0
    
    # ColorRamp para diferentes tonos de verde
    color_ramp.color_ramp.elements[0].color = (0.05, 0.3, 0.05, 1.0)  # Verde muy oscuro
    color_ramp.color_ramp.elements[1].color = (0.15, 0.5, 0.15, 1.0)  # Verde medio
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise.inputs['Vector'])
    links.new(noise.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Aplicar material a todas las capas
    for capa in capas:
        capa.data.materials.append(mat_hojas)
    
    return capas

# Función para crear luces navideñas
def crear_luces_navidenas():
    luces = []
    colores_luces = [
        (1.0, 0.1, 0.1, 1.0),  # Rojo
        (0.1, 1.0, 0.1, 1.0),  # Verde
        (0.1, 0.1, 1.0, 1.0),  # Azul
        (1.0, 1.0, 0.1, 1.0),  # Amarillo
        (1.0, 0.5, 0.1, 1.0),  # Naranja
        (1.0, 0.1, 1.0, 1.0),  # Magenta
    ]
    
    # Crear luces en espiral alrededor del árbol
    for i in range(50):
        # Calcular posición en espiral
        altura = 2.5 + (i / 50) * 6  # De abajo hacia arriba
        angulo = (i / 50) * 8 * math.pi  # Múltiples vueltas
        radio = 3.5 - (i / 50) * 2.5  # Radio decreciente hacia arriba
        
        pos_x = radio * math.cos(angulo)
        pos_y = radio * math.sin(angulo)
        pos_z = altura
        
        # Crear bombilla
        bpy.ops.mesh.primitive_ico_sphere_add(radius=0.1, enter_editmode=False,
                                            location=(pos_x, pos_y, pos_z))
        bombilla = bpy.context.active_object
        bombilla.name = f"Luz_Navidad_{i}"
        
        # Material emisivo para la bombilla
        color_luz = random.choice(colores_luces)
        mat_luz = bpy.data.materials.new(name=f"Material_Luz_{i}")
        mat_luz.use_nodes = True
        nodes = mat_luz.node_tree.nodes
        links = mat_luz.node_tree.links
        
        # Limpiar nodos existentes
        for node in nodes:
            nodes.remove(node)
        
        # Crear nodos para luz emisiva
        output = nodes.new(type='ShaderNodeOutputMaterial')
        emission = nodes.new(type='ShaderNodeEmission')
        
        # Configurar emisión
        emission.inputs['Color'].default_value = color_luz
        emission.inputs['Strength'].default_value = 5.0
        
        # Conectar nodos
        links.new(emission.outputs['Emission'], output.inputs['Surface'])
        
        # Aplicar material
        bombilla.data.materials.append(mat_luz)
        
        # Añadir luz puntual para iluminación real
        bpy.ops.object.light_add(type='POINT', radius=0.1, location=(pos_x, pos_y, pos_z))
        luz_punto = bpy.context.active_object
        luz_punto.name = f"Luz_Punto_{i}"
        luz_punto.data.energy = 10
        luz_punto.data.color = color_luz[:3]  # Solo RGB, sin alpha
        
        luces.append((bombilla, luz_punto))
    
    return luces

# Función para crear adornos navideños
def crear_adornos():
    adornos = []
    
    # Crear bolas navideñas
    for i in range(25):
        # Posición aleatoria en el árbol
        altura = random.uniform(2.5, 8)
        angulo = random.uniform(0, 2 * math.pi)
        radio = random.uniform(1, 3.5 - (altura - 2.5) * 0.4)  # Más cerca del centro en la parte superior
        
        pos_x = radio * math.cos(angulo)
        pos_y = radio * math.sin(angulo)
        pos_z = altura
        
        # Crear bola
        bpy.ops.mesh.primitive_ico_sphere_add(radius=random.uniform(0.15, 0.25),
                                            enter_editmode=False,
                                            location=(pos_x, pos_y, pos_z))
        bola = bpy.context.active_object
        bola.name = f"Bola_Navidad_{i}"
        
        # Material metálico para la bola
        mat_bola = bpy.data.materials.new(name=f"Material_Bola_{i}")
        mat_bola.use_nodes = True
        nodes = mat_bola.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        
        # Colores variados para las bolas
        colores_bolas = [
            (0.8, 0.1, 0.1, 1.0),  # Rojo
            (0.9, 0.7, 0.1, 1.0),  # Dorado
            (0.7, 0.7, 0.9, 1.0),  # Plateado
            (0.1, 0.1, 0.8, 1.0),  # Azul
        ]
        
        color_bola = random.choice(colores_bolas)
        bsdf.inputs[0].default_value = color_bola
        bsdf.inputs[4].default_value = 0.9  # Metálico
        bsdf.inputs[7].default_value = 0.1  # Rugosidad baja (brillante)
        
        bola.data.materials.append(mat_bola)
        adornos.append(bola)
    
    return adornos

# Función para crear la estrella en la punta
def crear_estrella():
    # Crear estrella de 5 puntas
    bpy.ops.mesh.primitive_cylinder_add(vertices=5, radius=0.5, depth=0.1,
                                      enter_editmode=False, location=(0, 0, 9))
    estrella = bpy.context.active_object
    estrella.name = "Estrella_Punta"
    
    # Deformar para hacer forma de estrella
    bpy.context.view_layer.objects.active = estrella
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.inset_faces(thickness=0.3)
    bpy.ops.transform.resize(value=(0.3, 0.3, 1))
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Material dorado brillante para la estrella
    mat_estrella = bpy.data.materials.new(name="Material_Estrella")
    mat_estrella.use_nodes = True
    nodes = mat_estrella.node_tree.nodes
    links = mat_estrella.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos para estrella dorada emisiva
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    emission = nodes.new(type='ShaderNodeEmission')
    mix_shader = nodes.new(type='ShaderNodeMixShader')
    
    # Configurar material dorado
    principled.inputs['Base Color'].default_value = (1.0, 0.8, 0.1, 1.0)  # Dorado
    principled.inputs['Metallic'].default_value = 1.0
    principled.inputs['Roughness'].default_value = 0.1
    
    # Configurar emisión
    emission.inputs['Color'].default_value = (1.0, 0.9, 0.5, 1.0)
    emission.inputs['Strength'].default_value = 2.0
    
    # Mezclar materiales
    mix_shader.inputs['Fac'].default_value = 0.3
    
    # Conectar nodos
    links.new(principled.outputs['BSDF'], mix_shader.inputs[1])
    links.new(emission.outputs['Emission'], mix_shader.inputs[2])
    links.new(mix_shader.outputs['Shader'], output.inputs['Surface'])
    
    # Aplicar material
    estrella.data.materials.append(mat_estrella)
    
    # Añadir luz para la estrella
    bpy.ops.object.light_add(type='POINT', radius=0.5, location=(0, 0, 9))
    luz_estrella = bpy.context.active_object
    luz_estrella.name = "Luz_Estrella"
    luz_estrella.data.energy = 50
    luz_estrella.data.color = (1.0, 0.9, 0.5)
    
    return estrella, luz_estrella

# Función para crear la base del árbol
def crear_base():
    # Maceta o base
    bpy.ops.mesh.primitive_cylinder_add(radius=1.2, depth=0.8, enter_editmode=False, location=(0, 0, 0.4))
    base = bpy.context.active_object
    base.name = "Base_Arbol"
    
    # Material para la base
    mat_base = bpy.data.materials.new(name="Material_Base")
    mat_base.use_nodes = True
    nodes = mat_base.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.6, 0.3, 0.1, 1.0)  # Marrón claro
    bsdf.inputs[7].default_value = 0.7  # Rugosidad
    base.data.materials.append(mat_base)
    
    return base

# Función para crear regalos alrededor del árbol
def crear_regalos():
    regalos = []
    
    for i in range(8):
        # Posición aleatoria alrededor del árbol
        angulo = (i / 8) * 2 * math.pi + random.uniform(-0.5, 0.5)
        distancia = random.uniform(4, 6)
        
        pos_x = distancia * math.cos(angulo)
        pos_y = distancia * math.sin(angulo)
        pos_z = random.uniform(0.3, 0.8)
        
        # Crear caja de regalo
        bpy.ops.mesh.primitive_cube_add(size=1, enter_editmode=False,
                                      location=(pos_x, pos_y, pos_z))
        regalo = bpy.context.active_object
        regalo.name = f"Regalo_{i}"
        regalo.scale = (random.uniform(0.8, 1.5), 
                       random.uniform(0.8, 1.5), 
                       random.uniform(0.6, 1.2))
        
        # Material colorido para regalo
        mat_regalo = bpy.data.materials.new(name=f"Material_Regalo_{i}")
        mat_regalo.use_nodes = True
        nodes = mat_regalo.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        
        colores_regalo = [
            (0.8, 0.1, 0.1, 1.0),  # Rojo
            (0.1, 0.8, 0.1, 1.0),  # Verde
            (0.1, 0.1, 0.8, 1.0),  # Azul
            (0.8, 0.8, 0.1, 1.0),  # Amarillo
            (0.8, 0.1, 0.8, 1.0),  # Magenta
        ]
        
        color_regalo = random.choice(colores_regalo)
        bsdf.inputs[0].default_value = color_regalo
        bsdf.inputs[7].default_value = 0.3  # Rugosidad baja (papel brillante)
        
        regalo.data.materials.append(mat_regalo)
        
        # Crear lazo del regalo
        bpy.ops.mesh.primitive_torus_add(major_radius=0.3, minor_radius=0.05,
                                       enter_editmode=False,
                                       location=(pos_x, pos_y, pos_z + regalo.scale.z + 0.1))
        lazo = bpy.context.active_object
        lazo.name = f"Lazo_{i}"
        
        # Material dorado para el lazo
        mat_lazo = bpy.data.materials.new(name=f"Material_Lazo_{i}")
        mat_lazo.use_nodes = True
        nodes = mat_lazo.node_tree.nodes
        bsdf = nodes.get("Principled BSDF")
        bsdf.inputs[0].default_value = (1.0, 0.8, 0.1, 1.0)  # Dorado
        bsdf.inputs[4].default_value = 0.8  # Metálico
        bsdf.inputs[7].default_value = 0.2  # Rugosidad
        lazo.data.materials.append(mat_lazo)
        
        regalos.append((regalo, lazo))
    
    return regalos

# Crear todos los elementos del árbol de Navidad
tronco = crear_tronco()
capas = crear_capas_arbol()
luces = crear_luces_navidenas()
adornos = crear_adornos()
estrella, luz_estrella = crear_estrella()
base = crear_base()
regalos = crear_regalos()

# Crear nieve en el suelo
def crear_nieve():
    # Plano base para la nieve
    bpy.ops.mesh.primitive_plane_add(size=20, enter_editmode=False, location=(0, 0, 0))
    nieve = bpy.context.active_object
    nieve.name = "Nieve_Suelo"

    # Material para nieve
    mat_nieve = bpy.data.materials.new(name="Material_Nieve")
    mat_nieve.use_nodes = True
    nodes = mat_nieve.node_tree.nodes
    links = mat_nieve.node_tree.links

    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)

    # Crear nodos para nieve brillante
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    noise = nodes.new(type='ShaderNodeTexNoise')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')

    # Configurar propiedades de la nieve
    principled.inputs['Base Color'].default_value = (0.95, 0.95, 1.0, 1.0)  # Blanco azulado
    principled.inputs['Roughness'].default_value = 0.3
    principled.inputs['Specular'].default_value = 0.8
    principled.inputs['Subsurface'].default_value = 0.1  # Subsuperficie para realismo

    # Configurar ruido para textura
    noise.inputs['Scale'].default_value = 50.0
    noise.inputs['Detail'].default_value = 10.0

    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise.inputs['Vector'])
    links.new(noise.outputs['Fac'], principled.inputs['Roughness'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])

    # Aplicar material
    nieve.data.materials.append(mat_nieve)

    return nieve

nieve = crear_nieve()

# Añadir iluminación navideña
# Luz principal (luna/ambiente nocturno)
bpy.ops.object.light_add(type='SUN', radius=1, location=(10, 10, 20))
luz_principal = bpy.context.active_object
luz_principal.name = "Luz_Principal"
luz_principal.data.energy = 2
luz_principal.data.color = (0.8, 0.9, 1.0)  # Luz azulada fría
luz_principal.rotation_euler = (math.radians(45), math.radians(30), 0)

# Luz de relleno cálida
bpy.ops.object.light_add(type='AREA', radius=1, location=(-5, -5, 8))
luz_relleno = bpy.context.active_object
luz_relleno.name = "Luz_Relleno"
luz_relleno.data.energy = 100
luz_relleno.data.size = 5
luz_relleno.data.color = (1.0, 0.8, 0.6)  # Luz cálida

# Añadir cámara con vista del árbol
bpy.ops.object.camera_add(enter_editmode=False, align='VIEW',
                        location=(8, -8, 6),
                        rotation=(math.radians(60), 0, math.radians(45)))
camara = bpy.context.active_object
camara.name = "Camara_Arbol_Navidad"
bpy.context.scene.camera = camara

# Configurar fondo navideño nocturno
world = bpy.context.scene.world
world.use_nodes = True
nodes = world.node_tree.nodes
links = world.node_tree.links

# Limpiar nodos existentes
for node in nodes:
    nodes.remove(node)

# Crear nodos para cielo nocturno navideño
background = nodes.new(type='ShaderNodeBackground')
output = nodes.new(type='ShaderNodeOutputWorld')
tex_coord = nodes.new(type='ShaderNodeTexCoord')
mapping = nodes.new(type='ShaderNodeMapping')
noise_texture = nodes.new(type='ShaderNodeTexNoise')
color_ramp = nodes.new(type='ShaderNodeValToRGB')
mix_rgb = nodes.new(type='ShaderNodeMixRGB')

# Configurar fondo nocturno
background.inputs['Color'].default_value = (0.05, 0.05, 0.15, 1.0)  # Azul muy oscuro
background.inputs['Strength'].default_value = 0.8

# Configurar ruido para estrellas
noise_texture.inputs['Scale'].default_value = 800.0
noise_texture.inputs['Detail'].default_value = 10.0

# ColorRamp para estrellas navideñas
color_ramp.color_ramp.elements[0].position = 0.97
color_ramp.color_ramp.elements[0].color = (0.0, 0.0, 0.0, 1.0)
color_ramp.color_ramp.elements[1].position = 1.0
color_ramp.color_ramp.elements[1].color = (1.0, 1.0, 1.0, 1.0)

# Configurar mezcla
mix_rgb.blend_type = 'ADD'
mix_rgb.inputs['Fac'].default_value = 1.0
mix_rgb.inputs[1].default_value = (0.05, 0.05, 0.15, 1.0)

# Conectar nodos
links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
links.new(mapping.outputs['Vector'], noise_texture.inputs['Vector'])
links.new(noise_texture.outputs['Fac'], color_ramp.inputs['Fac'])
links.new(color_ramp.outputs['Color'], mix_rgb.inputs[2])
links.new(mix_rgb.outputs['Color'], background.inputs['Color'])
links.new(background.outputs['Background'], output.inputs['Surface'])

# Función para animar las luces navideñas (parpadeo)
def animar_luces():
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = 120

    for i, (bombilla, luz_punto) in enumerate(luces):
        # Crear animación de parpadeo aleatorio
        for frame in range(1, 121, 10):
            # Intensidad aleatoria
            intensidad = random.choice([0.5, 1.0, 1.5, 2.0])

            # Keyframe para la bombilla
            for node in bombilla.data.materials[0].node_tree.nodes:
                if node.type == 'EMISSION':
                    node.inputs['Strength'].default_value = intensidad * 5
                    node.inputs['Strength'].keyframe_insert(data_path="default_value", frame=frame)

            # Keyframe para la luz
            luz_punto.data.energy = intensidad * 10
            luz_punto.data.keyframe_insert(data_path="energy", frame=frame)

# Animar las luces
animar_luces()

print("¡Árbol de Navidad 3D detallado creado con éxito!")
print("Incluye: tronco, capas de hojas, luces navideñas parpadeantes, adornos, estrella, regalos y nieve")
print("Las luces parpadean automáticamente - presiona Espacio para ver la animación")
print("Para renderizar, presiona F12 o usa el menú Render > Render Image")
