# Calculadora de Fuerza de Lorentz

## Descripción

Programa educativo completo para resolver ejercicios de física electromagnética relacionados con partículas cargadas en campos eléctricos y magnéticos. Incluye interfaz gráfica intuitiva, simulaciones de trayectorias, y herramientas de análisis avanzado.

## Características Principales

### 🆕 **NUEVA FUNCIONALIDAD: Entrada por Texto**
- 📝 **Copia y pega** problemas directamente desde libros/exámenes
- 🤖 **Reconocimiento automático** de datos y parámetros
- 🔍 **Procesamiento inteligente** de texto en lenguaje natural
- ⚡ **Solución instantánea** sin entrada manual de datos

### 🔬 Cálculos Físicos
- **Fuerza de Lorentz**: F = q(E + v × B)
- **Solo campo eléctrico**: F = qE
- **Solo campo magnético**: F = q(v × B)
- **Campos combinados**: Análisis completo de interacciones

### 📊 Tipos de Ejercicios Cubiertos
1. **Campo Eléctrico Uniforme**
   - Movimiento rectilíneo acelerado
   - Trayectorias parabólicas
   - Cálculo de trabajo y energía

2. **Campo Magnético Uniforme**
   - Movimiento circular
   - Radio de curvatura y período ciclotrónico
   - Frecuencia de resonancia

3. **Campos Combinados**
   - Selector de velocidad
   - Deriva E×B
   - Trayectorias complejas

4. **Movimientos Especiales**
   - Movimiento helicoidal
   - Deflexión magnética
   - Confinamiento de partículas

### 🎯 Análisis Avanzado
- **Trayectorias**: Simulación 3D de movimiento
- **Energética**: Análisis de trabajo y conservación
- **Vectorial**: Descomposición y productos vectoriales
- **Comparativo**: Análisis de diferentes configuraciones

### 🛠️ Herramientas Incluidas
- **Calculadora Vectorial**: Operaciones con vectores 3D
- **Conversor de Unidades**: Energía, velocidad, campos
- **Ejercicios Predefinidos**: 8+ ejemplos típicos
- **Generador Aleatorio**: Problemas con valores aleatorios

## Instalación

### Requisitos del Sistema
- Python 3.7 o superior
- Sistema operativo: Windows, macOS, o Linux

### Instalación Automática
```bash
# 1. Ejecutar el instalador de dependencias
python instalar_dependencias.py

# 2. Ejecutar el programa principal
python fuerza_lorentz_main.py
```

### Instalación Manual
```bash
# Instalar dependencias
pip install numpy matplotlib

# Ejecutar programa
python fuerza_lorentz_main.py
```

## Estructura del Proyecto

```
FÍSICA/
├── ejecutar_programa.py        # 🚀 INICIO RÁPIDO - Selector de interfaces
├── interfaz_texto.py           # 📝 NUEVA: Interfaz con entrada de texto
├── procesador_texto.py         # 🤖 Motor de procesamiento de lenguaje natural
├── fuerza_lorentz_main.py      # 🖥️ Interfaz gráfica tradicional
├── lorentz_physics.py          # 🔬 Módulo de cálculos físicos
├── vector_utils.py             # ➡️ Utilidades vectoriales
├── gui_components.py           # 🎨 Componentes de interfaz
├── ejercicios_ejemplos.py      # 📚 Ejercicios predefinidos
├── ejercicios_adicionales.py   # 📖 Problemas avanzados
├── test_programa.py            # 🧪 Suite de pruebas
├── test_procesador_texto.py    # 🔍 Pruebas específicas de texto
├── demo_simple.py              # 🎯 Demo sin interfaz gráfica
├── instalar_dependencias.py    # ⚙️ Instalador automático
├── requirements.txt            # 📋 Lista de dependencias
├── README.md                   # 📖 Este archivo
└── GUIA_ENTRADA_TEXTO.md       # 📝 Guía detallada de entrada por texto
```

## 🚀 Inicio Rápido

### Ejecutar el Programa
```bash
python ejecutar_programa.py
```

**Selecciona tu interfaz preferida:**
1. **📝 Entrada por TEXTO** (¡NUEVO!) - Copia y pega problemas
2. **🖥️ Interfaz tradicional** - Entrada manual de datos
3. **🧪 Demo simple** - Ejemplos básicos

## 📝 Nueva Funcionalidad: Entrada por Texto

### ¿Cómo Funciona?
1. **Copia** el texto del problema desde cualquier fuente
2. **Pega** en el área de texto del programa
3. **Presiona** "Analizar Problema" para extraer datos
4. **Obtén** la solución completa automáticamente

### Ejemplo de Uso
```
Texto de entrada:
"Un protón con velocidad v = (2×10⁵) î m/s se mueve en campos
E = (3×10³) ĵ V/m y B = (0.02) k̂ T. Calcula todas las fuerzas."

Resultado automático:
✅ Partícula: Protón identificado
✅ Velocidad: (200000, 0, 0) m/s
✅ Campo E: (0, 3000, 0) V/m
✅ Campo B: (0, 0, 0.02) T
✅ Solución completa con todos los cálculos
```

**📖 Para más detalles, consulta:** `GUIA_ENTRADA_TEXTO.md`

## Uso del Programa

### Interfaz Principal (Modo Tradicional)

#### 1. Pestaña "Cálculo Principal"
- **Entrada de Partícula**: Tipo (electrón/protón/personalizada), carga, masa, velocidad
- **Campos Electromagnéticos**: Vectores E y B en 3D
- **Botones de Cálculo**: 
  - "Calcular Fuerza": Fuerza de Lorentz básica
  - "Análisis Completo": Análisis detallado del sistema
- **Resultados**: Texto detallado con todos los cálculos
- **Gráficos**: Visualización 3D de vectores

#### 2. Pestaña "Análisis Avanzado"
- **Simulación de Trayectorias**: Parámetros de tiempo y pasos
- **Análisis Energético**: Trabajo, energía cinética, conservación
- **Resultados Avanzados**: Información detallada de simulaciones

#### 3. Pestaña "Trayectorias"
- **Tipos de Movimiento**: Rectilíneo, circular, helicoidal, complejo
- **Visualización 3D**: Gráficos de trayectorias en tiempo real
- **Comparación**: Análisis de diferentes casos

### Menús Disponibles

#### Menú "Ejercicios"
- 8 ejercicios predefinidos típicos de exámenes
- Generador de problemas aleatorios
- Carga automática de parámetros

#### Menú "Herramientas"
- **Calculadora Vectorial**: Suma, resta, producto punto, producto cruz, ángulos
- **Conversor de Unidades**: Energía (J, eV, keV, MeV), velocidad, campos magnéticos

#### Menú "Ayuda"
- **Fórmulas**: Referencia completa de ecuaciones
- **Acerca de**: Información del programa

## Ejemplos de Ejercicios

### 1. Electrón en Campo Eléctrico
```
Partícula: Electrón (q = -1.602×10⁻¹⁹ C, m = 9.109×10⁻³¹ kg)
Velocidad inicial: (1×10⁶, 0, 0) m/s
Campo eléctrico: (0, 1000, 0) V/m
Resultado: Trayectoria parabólica
```

### 2. Protón en Campo Magnético
```
Partícula: Protón (q = +1.602×10⁻¹⁹ C, m = 1.673×10⁻²⁷ kg)
Velocidad inicial: (1×10⁵, 0, 0) m/s
Campo magnético: (0, 0, 0.1) T
Resultado: Movimiento circular, r = 1.04×10⁻² m
```

### 3. Selector de Velocidad
```
Campos cruzados: E = (0, 1000, 0) V/m, B = (0, 0, 0.01) T
Velocidad seleccionada: v = E/B = 1×10⁵ m/s
Trayectoria rectilínea para v = 1×10⁵ m/s
```

## Fórmulas Principales

### Fuerza de Lorentz
```
F = q(E + v × B)
```

### Movimiento Circular en Campo B
```
Radio: r = mv/(qB)
Período: T = 2πm/(qB)
Frecuencia: f = qB/(2πm)
```

### Trabajo y Energía
```
Trabajo eléctrico: W = qE·d
Trabajo magnético: W = 0 (siempre)
Energía cinética: K = ½mv²
```

## Aplicaciones Educativas

### Para Estudiantes
- Visualización de conceptos abstractos
- Verificación de cálculos manuales
- Exploración de diferentes escenarios
- Preparación para exámenes

### Para Profesores
- Herramienta de demostración en clase
- Generación de ejercicios variados
- Verificación rápida de resultados
- Material de apoyo visual

## Casos de Uso Típicos

1. **Tubo de Rayos Catódicos**: Deflexión de electrones
2. **Espectrómetro de Masas**: Separación de iones
3. **Ciclotron**: Aceleración de partículas
4. **Confinamiento Magnético**: Plasma y fusión
5. **Detectores de Partículas**: Física de altas energías
6. **Auroras Boreales**: Interacción con campo terrestre

## Limitaciones

- Física clásica (no relativista para v << c)
- Campos uniformes y estáticos
- Sin efectos cuánticos
- Sin radiación electromagnética

## Contribuciones

Este es un proyecto educativo. Las mejoras sugeridas incluyen:
- Efectos relativistas
- Campos no uniformes
- Más tipos de partículas
- Exportación de datos
- Animaciones en tiempo real

## Licencia

Programa educativo de uso libre para fines académicos.

---

**¡Disfruta explorando la física electromagnética!** 🚀⚡🧲
