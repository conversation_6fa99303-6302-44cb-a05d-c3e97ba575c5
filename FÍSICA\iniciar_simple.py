"""
Script de inicio simple y directo para la calculadora de Lorentz
"""

import sys
import os

def verificar_tkinter():
    """Verifica que tkinter esté disponible"""
    try:
        import tkinter
        return True
    except ImportError:
        return False

def main():
    """Función principal simplificada"""
    print("🔬 CALCULADORA DE FUERZA DE LORENTZ")
    print("=" * 50)
    print("Programa educativo con entrada de texto")
    print("=" * 50)
    
    # Verificar tkinter
    if not verificar_tkinter():
        print("❌ Error: tkinter no está disponible")
        print("tkinter viene incluido con Python por defecto")
        print("Si usas Linux, instala: sudo apt-get install python3-tk")
        return
    
    print("✅ Sistema compatible detectado")
    print("\n🚀 Iniciando programa...")
    
    try:
        # Importar y ejecutar el programa simple
        from programa_texto_simple import main as ejecutar_programa
        ejecutar_programa()
        
    except ImportError as e:
        print(f"❌ Error de importación: {e}")
        print("Verifica que el archivo 'programa_texto_simple.py' esté en la misma carpeta")
        
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        print("\nIntenta ejecutar directamente:")
        print("python programa_texto_simple.py")

if __name__ == "__main__":
    main()
