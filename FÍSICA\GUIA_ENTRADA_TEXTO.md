# 📝 Guía de Entrada por Texto - Calculadora de Fuerza de Lorentz

## 🎯 ¿Qué es la Entrada por Texto?

La nueva funcionalidad de **entrada por texto** permite copiar y pegar problemas de física directamente desde libros, exámenes, PDFs o imágenes, y el programa automáticamente:

- 🔍 **Reconoce** los datos del problema
- 📊 **Extrae** valores numéricos y unidades
- 🧮 **Calcula** todas las magnitudes solicitadas
- 📈 **Visualiza** los resultados

## 🚀 Cómo Usar

### Paso 1: Ejecutar el Programa
```bash
python ejecutar_programa.py
```
Selecciona la opción **1** (Interfaz con entrada de TEXTO)

### Paso 2: Copiar y Pegar
Simplemente copia el texto del problema y pégalo en el área de texto.

### Paso 3: Analizar y Resolver
1. Presiona **"🔍 Analizar Problema"** para extraer los datos
2. Presiona **"🧮 Resolver"** para obtener la solución completa

## 📋 Formatos Soportados

### ✅ Vectores Reconocidos

#### Formato con Vectores Unitarios
```
v = (2 × 10⁵) î m/s
E = (3 × 10³) ĵ V/m  
B = (0.02) k̂ T
```

#### Formato con Paréntesis
```
v = (1e5, 2e5, 0) m/s
E = (0, 1000, 500) V/m
B = (0.1, 0, 0.05) T
```

#### Formato Simple (asume dirección X)
```
velocidad = 1e6 m/s
campo eléctrico = 5000 V/m
```

### ✅ Notaciones Numéricas

#### Notación Científica
```
2 × 10⁵  →  2e5
1.6 × 10⁻¹⁹  →  1.6e-19
3 * 10^3  →  3e3
```

#### Números Decimales
```
200000 m/s
0.02 T
1.67e-27 kg
```

### ✅ Partículas Reconocidas
- **Electrón**: "electrón", "electron", "e-"
- **Protón**: "protón", "proton", "p+"
- **Ion**: "ion", "ión"
- **Partícula personalizada**: cualquier otro término

### ✅ Unidades Soportadas

#### Velocidad
- `m/s` (metros por segundo)
- `km/s` (kilómetros por segundo)
- `km/h` (kilómetros por hora)
- `c` (fracción de velocidad de luz)

#### Campo Eléctrico
- `V/m` (voltios por metro)
- `kV/m` (kilovoltios por metro)
- `N/C` (newtons por coulomb)

#### Campo Magnético
- `T` (tesla)
- `mT` (militesla)
- `μT` (microtesla)
- `G` (gauss)
- `mG` (miligauss)

#### Carga
- `C` (coulomb)
- `e` (carga elemental)
- `mC`, `μC`, `nC` (submúltiplos)

#### Masa
- `kg` (kilogramo)
- `g` (gramo)
- `u` (unidad de masa atómica)
- `MeV/c²` (megaelectronvoltio sobre c²)

## 📚 Ejemplos Prácticos

### Ejemplo 1: Problema Típico de Examen
```
Un protón con una velocidad de v = (2 × 10⁵) î m/s se mueve en una región donde hay un campo eléctrico E = (3 × 10³) ĵ V/m y un campo magnético B = (0.02) k̂ T. Calcula la fuerza eléctrica, la fuerza magnética, la fuerza total que actúa sobre el protón, la aceleración resultante, determina el tipo de movimiento que describe la partícula y, en el caso de que se elimine el campo eléctrico, calcula el radio de la trayectoria circular que seguiría.
```

**Resultado Automático:**
- ✅ Partícula: Protón identificado
- ✅ Velocidad: (2×10⁵, 0, 0) m/s
- ✅ Campo E: (0, 3×10³, 0) V/m
- ✅ Campo B: (0, 0, 0.02) T
- ✅ Preguntas: Fuerza eléctrica, magnética, total, aceleración, tipo de movimiento, radio

### Ejemplo 2: Problema Simple
```
Un electrón con velocidad de 1×10⁶ m/s entra en un campo magnético de 0.1 T perpendicular a su movimiento. Calcula el radio de la órbita.
```

**Resultado Automático:**
- ✅ Partícula: Electrón
- ✅ Velocidad: (1×10⁶, 0, 0) m/s
- ✅ Campo B: (0, 0, 0.1) T
- ✅ Pregunta: Radio de curvatura

### Ejemplo 3: Problema con Datos Explícitos
```
Una partícula con carga q = 1.6×10⁻¹⁹ C y masa m = 1.67×10⁻²⁷ kg tiene velocidad v = (1×10⁵, 1×10⁵, 2×10⁵) m/s en un campo magnético B = (0, 0, 0.5) T. Analiza el movimiento helicoidal.
```

**Resultado Automático:**
- ✅ Partícula: Personalizada con carga y masa especificadas
- ✅ Velocidad: (1×10⁵, 1×10⁵, 2×10⁵) m/s
- ✅ Campo B: (0, 0, 0.5) T
- ✅ Análisis: Movimiento helicoidal

## 🔍 Preguntas Reconocidas Automáticamente

El programa identifica qué calcular basándose en palabras clave:

| Palabra Clave | Cálculo |
|---------------|---------|
| "fuerza eléctrica" | F_E = qE |
| "fuerza magnética" | F_B = q(v×B) |
| "fuerza total" | F = F_E + F_B |
| "aceleración" | a = F/m |
| "radio" | r = mv/(qB) |
| "período" | T = 2πm/(qB) |
| "frecuencia" | f = 1/T |
| "trayectoria", "movimiento" | Análisis de tipo de movimiento |
| "trabajo" | W = qE·d |
| "energía" | K = ½mv² |

## 🎯 Consejos para Mejores Resultados

### ✅ Recomendado
- Incluir unidades siempre que sea posible
- Usar notación vectorial clara (î, ĵ, k̂)
- Especificar el tipo de partícula
- Incluir valores de carga y masa si son no estándar

### ⚠️ Evitar
- Texto muy ambiguo sin datos numéricos
- Mezclar diferentes sistemas de unidades
- Omitir información crucial como la dirección de los campos

## 🛠️ Solución de Problemas

### Problema: "No se reconocen los datos"
**Solución:** Verifica que:
- Los números estén en formato correcto (1e5, 2×10³)
- Las unidades estén especificadas
- Los vectores usen î, ĵ, k̂ o paréntesis

### Problema: "Partícula no identificada"
**Solución:** 
- Usa "electrón" o "protón" explícitamente
- O especifica carga y masa manualmente

### Problema: "Cálculos incorrectos"
**Solución:**
- Verifica que los datos extraídos sean correctos en la pestaña "Datos Extraídos"
- Ajusta el texto si es necesario

## 🎓 Casos de Uso Educativos

### Para Estudiantes
1. **Tareas**: Copia problemas del libro y obtén soluciones paso a paso
2. **Exámenes**: Practica con problemas similares a los de examen
3. **Verificación**: Comprueba tus cálculos manuales

### Para Profesores
1. **Preparación**: Genera soluciones rápidas para problemas de clase
2. **Verificación**: Confirma resultados de ejercicios propuestos
3. **Demostración**: Muestra cálculos en tiempo real

## 🔬 Limitaciones Actuales

- Solo reconoce problemas en español e inglés básico
- Campos deben ser uniformes y estáticos
- No maneja efectos relativistas
- Requiere que los datos estén explícitos en el texto

## 🚀 Próximas Mejoras

- Reconocimiento de imágenes (OCR)
- Soporte para más idiomas
- Reconocimiento de diagramas
- Exportación de soluciones a PDF

---

**¡Disfruta de la nueva funcionalidad de entrada por texto!** 🎉

Para más ayuda, consulta el archivo `README.md` o ejecuta los ejemplos incluidos.
