"""
Prueba simple para verificar que el programa funciona
"""

def test_basico():
    """Prueba básica de funcionamiento"""
    print("=== PRUEBA BÁSICA ===")
    
    # Verificar importaciones básicas
    try:
        import tkinter as tk
        print("✅ tkinter disponible")
    except ImportError:
        print("❌ tkinter no disponible")
        return False
    
    try:
        import math
        import re
        print("✅ Módulos estándar disponibles")
    except ImportError:
        print("❌ Módulos estándar no disponibles")
        return False
    
    # Probar cálculos básicos
    try:
        # Datos del ejemplo
        q = 1.6e-19  # Carga del protón
        m = 1.67e-27  # Masa del protón
        v = [2e5, 0, 0]  # Velocidad
        E = [0, 3e3, 0]  # Campo eléctrico
        B = [0, 0, 0.02]  # Campo magnético
        
        # Fuerza eléctrica
        F_E = [q * E[0], q * E[1], q * E[2]]
        print(f"✅ Fuerza eléctrica calculada: {F_E}")
        
        # Producto cruz v × B
        vxB = [
            v[1]*B[2] - v[2]*B[1],
            v[2]*B[0] - v[0]*B[2],
            v[0]*B[1] - v[1]*B[0]
        ]
        
        # Fuerza magnética
        F_B = [q * vxB[0], q * vxB[1], q * vxB[2]]
        print(f"✅ Fuerza magnética calculada: {F_B}")
        
        # Radio de curvatura
        mag_v = math.sqrt(v[0]**2 + v[1]**2 + v[2]**2)
        mag_B = math.sqrt(B[0]**2 + B[1]**2 + B[2]**2)
        radio = (m * mag_v) / (abs(q) * mag_B)
        print(f"✅ Radio calculado: {radio:.3e} m")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en cálculos: {e}")
        return False

def test_extraccion_texto():
    """Prueba extracción de datos de texto"""
    print("\n=== PRUEBA EXTRACCIÓN DE TEXTO ===")
    
    try:
        import re
        
        texto = "Un protón con velocidad v = (2 × 10⁵) î m/s en campo B = (0.02) k̂ T"
        
        # Convertir notación científica
        texto_procesado = re.sub(r'(\d+(?:\.\d+)?)\s*×\s*10\^?([+-]?\d+)', r'\1e\2', texto)
        print(f"✅ Texto procesado: {texto_procesado}")
        
        # Extraer números
        patron_numero = r'([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)'
        numeros = re.findall(patron_numero, texto_procesado)
        print(f"✅ Números encontrados: {numeros}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en extracción: {e}")
        return False

def test_interfaz_minima():
    """Prueba creación de interfaz mínima"""
    print("\n=== PRUEBA INTERFAZ MÍNIMA ===")
    
    try:
        import tkinter as tk
        
        # Crear ventana de prueba
        root = tk.Tk()
        root.title("Prueba")
        root.geometry("300x200")
        root.withdraw()  # Ocultar ventana
        
        # Crear widgets básicos
        label = tk.Label(root, text="Prueba")
        entry = tk.Entry(root)
        button = tk.Button(root, text="Test")
        
        print("✅ Widgets básicos creados")
        
        # Limpiar
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ Error en interfaz: {e}")
        return False

def main():
    """Ejecuta todas las pruebas"""
    print("PRUEBAS BÁSICAS DEL SISTEMA")
    print("=" * 40)
    
    pruebas = [
        ("Funcionamiento básico", test_basico),
        ("Extracción de texto", test_extraccion_texto),
        ("Interfaz mínima", test_interfaz_minima)
    ]
    
    exitosas = 0
    
    for nombre, prueba in pruebas:
        try:
            if prueba():
                exitosas += 1
                print(f"✅ {nombre}: EXITOSA")
            else:
                print(f"❌ {nombre}: FALLIDA")
        except Exception as e:
            print(f"❌ {nombre}: ERROR - {e}")
    
    print("\n" + "=" * 40)
    print(f"RESULTADO: {exitosas}/{len(pruebas)} pruebas exitosas")
    
    if exitosas == len(pruebas):
        print("\n🎉 ¡Sistema funcionando correctamente!")
        print("Puedes ejecutar el programa con:")
        print("python iniciar_simple.py")
    else:
        print(f"\n⚠ {len(pruebas) - exitosas} pruebas fallaron")
        print("Revisa los errores antes de ejecutar el programa principal")
    
    return exitosas == len(pruebas)

if __name__ == "__main__":
    main()
