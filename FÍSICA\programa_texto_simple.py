"""
Programa simplificado de fuerza de Lorentz con entrada de texto
Interfaz gráfica sencilla y funcional
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import re
import math

class CalculadoraLorentzTexto:
    """Calculadora simple con entrada de texto"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Calculadora de Fuerza de Lorentz - Entrada por Texto")
        self.root.geometry("900x700")
        
        # Constantes físicas
        self.e = 1.602176634e-19  # Carga elemental
        self.me = 9.1093837015e-31  # Masa electrón
        self.mp = 1.67262192369e-27  # Masa protón
        
        self.crear_interfaz()
    
    def crear_interfaz(self):
        """Crea la interfaz gráfica simple"""
        # Título
        titulo = tk.Label(self.root, text="🔬 Calculadora de Fuerza de Lorentz", 
                         font=('Arial', 16, 'bold'), fg='blue')
        titulo.pack(pady=10)
        
        # Instrucciones
        instrucciones = tk.Label(self.root, 
                               text="📝 Copia y pega aquí el problema de física:",
                               font=('Arial', 12))
        instrucciones.pack(pady=5)
        
        # Área de texto para entrada
        self.texto_entrada = scrolledtext.ScrolledText(self.root, height=8, width=100, 
                                                      font=('Arial', 11), wrap=tk.WORD)
        self.texto_entrada.pack(padx=20, pady=10, fill=tk.X)
        
        # Ejemplo por defecto
        ejemplo = """Un protón con una velocidad de v = (2 × 10⁵) î m/s se mueve en una región donde hay un campo eléctrico E = (3 × 10³) ĵ V/m y un campo magnético B = (0.02) k̂ T. Calcula la fuerza eléctrica, la fuerza magnética, la fuerza total que actúa sobre el protón, la aceleración resultante, determina el tipo de movimiento que describe la partícula y, en el caso de que se elimine el campo eléctrico, calcula el radio de la trayectoria circular que seguiría. Usa los valores q = 1.6 × 10⁻¹⁹ C y m = 1.67 × 10⁻²⁷ kg."""
        
        self.texto_entrada.insert(tk.END, ejemplo)
        
        # Botones
        frame_botones = tk.Frame(self.root)
        frame_botones.pack(pady=10)
        
        btn_resolver = tk.Button(frame_botones, text="🧮 RESOLVER PROBLEMA", 
                               command=self.resolver_problema, 
                               font=('Arial', 12, 'bold'), 
                               bg='green', fg='white', padx=20)
        btn_resolver.pack(side=tk.LEFT, padx=10)
        
        btn_limpiar = tk.Button(frame_botones, text="🗑️ Limpiar", 
                              command=self.limpiar, 
                              font=('Arial', 12), padx=20)
        btn_limpiar.pack(side=tk.LEFT, padx=10)
        
        btn_ejemplo = tk.Button(frame_botones, text="📚 Cargar Ejemplo", 
                              command=self.cargar_ejemplo, 
                              font=('Arial', 12), padx=20)
        btn_ejemplo.pack(side=tk.LEFT, padx=10)
        
        # Área de resultados
        tk.Label(self.root, text="📊 Resultados:", font=('Arial', 12, 'bold')).pack(anchor='w', padx=20, pady=(20,5))
        
        self.texto_resultados = scrolledtext.ScrolledText(self.root, height=15, width=100, 
                                                         font=('Courier', 10), wrap=tk.WORD)
        self.texto_resultados.pack(padx=20, pady=10, fill=tk.BOTH, expand=True)
    
    def extraer_numero(self, texto, patron):
        """Extrae un número del texto usando un patrón"""
        # Convertir notación científica
        texto = re.sub(r'(\d+(?:\.\d+)?)\s*×\s*10\^?([+-]?\d+)', r'\1e\2', texto)
        texto = re.sub(r'(\d+(?:\.\d+)?)\s*\*\s*10\^?([+-]?\d+)', r'\1e\2', texto)
        
        match = re.search(patron, texto, re.IGNORECASE)
        if match:
            try:
                return float(match.group(1))
            except:
                return 0
        return 0
    
    def extraer_vector(self, texto, variable):
        """Extrae un vector del texto"""
        # Buscar formato con vectores unitarios (î, ĵ, k̂)
        patron_i = rf'{variable}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[îi]'
        patron_j = rf'{variable}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[ĵj]'
        patron_k = rf'{variable}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[k̂k]'
        
        x = self.extraer_numero(texto, patron_i)
        y = self.extraer_numero(texto, patron_j)
        z = self.extraer_numero(texto, patron_k)
        
        # Si no encuentra vectores unitarios, buscar formato (x, y, z)
        if x == 0 and y == 0 and z == 0:
            patron_parentesis = rf'{variable}[^=]*=\s*\(([^)]+)\)'
            match = re.search(patron_parentesis, texto, re.IGNORECASE)
            if match:
                componentes = match.group(1).split(',')
                if len(componentes) >= 3:
                    try:
                        x = float(re.sub(r'[^\d.e+-]', '', componentes[0]))
                        y = float(re.sub(r'[^\d.e+-]', '', componentes[1]))
                        z = float(re.sub(r'[^\d.e+-]', '', componentes[2]))
                    except:
                        pass
        
        # Si solo encuentra un número, asumir dirección x
        if x == 0 and y == 0 and z == 0:
            patron_simple = rf'{variable}[^=]*=\s*([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)'
            numero = self.extraer_numero(texto, patron_simple)
            if numero != 0:
                x = numero
        
        return [x, y, z]
    
    def identificar_particula(self, texto):
        """Identifica el tipo de partícula"""
        texto_lower = texto.lower()
        if 'protón' in texto_lower or 'proton' in texto_lower:
            return 'proton'
        elif 'electrón' in texto_lower or 'electron' in texto_lower:
            return 'electron'
        else:
            return 'personalizada'
    
    def resolver_problema(self):
        """Resuelve el problema de física"""
        texto = self.texto_entrada.get(1.0, tk.END)
        
        if not texto.strip():
            messagebox.showwarning("Advertencia", "Por favor ingresa un problema")
            return
        
        try:
            # Limpiar área de resultados
            self.texto_resultados.delete(1.0, tk.END)
            
            # Extraer datos
            tipo_particula = self.identificar_particula(texto)
            
            # Extraer vectores
            velocidad = self.extraer_vector(texto, 'v')
            campo_E = self.extraer_vector(texto, 'E')
            campo_B = self.extraer_vector(texto, 'B')
            
            # Extraer carga y masa si están especificadas
            carga = self.extraer_numero(texto, r'q\s*=\s*([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)')
            masa = self.extraer_numero(texto, r'm\s*=\s*([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)')
            
            # Usar valores por defecto según el tipo de partícula
            if tipo_particula == 'electron':
                if carga == 0: carga = -self.e
                if masa == 0: masa = self.me
            elif tipo_particula == 'proton':
                if carga == 0: carga = self.e
                if masa == 0: masa = self.mp
            else:
                if carga == 0: carga = self.e
                if masa == 0: masa = self.me
            
            # Mostrar datos extraídos
            self.mostrar_resultados(velocidad, campo_E, campo_B, carga, masa, tipo_particula)
            
        except Exception as e:
            messagebox.showerror("Error", f"Error al procesar el problema: {e}")
    
    def mostrar_resultados(self, v, E, B, q, m, tipo):
        """Muestra todos los resultados calculados"""
        resultado = "=" * 60 + "\n"
        resultado += "🔬 SOLUCIÓN DEL PROBLEMA DE FUERZA DE LORENTZ\n"
        resultado += "=" * 60 + "\n\n"
        
        # Datos extraídos
        resultado += "📋 DATOS EXTRAÍDOS:\n"
        resultado += f"   Tipo de partícula: {tipo.title()}\n"
        resultado += f"   Carga (q): {q:.3e} C\n"
        resultado += f"   Masa (m): {m:.3e} kg\n"
        resultado += f"   Velocidad (v): ({v[0]:.0f}, {v[1]:.0f}, {v[2]:.0f}) m/s\n"
        resultado += f"   Campo eléctrico (E): ({E[0]:.0f}, {E[1]:.0f}, {E[2]:.0f}) V/m\n"
        resultado += f"   Campo magnético (B): ({B[0]:.3f}, {B[1]:.3f}, {B[2]:.3f}) T\n\n"
        
        # Cálculos de fuerza
        resultado += "⚡ CÁLCULO DE FUERZAS:\n\n"
        
        # Fuerza eléctrica: F_E = qE
        F_E = [q * E[0], q * E[1], q * E[2]]
        mag_F_E = math.sqrt(F_E[0]**2 + F_E[1]**2 + F_E[2]**2)
        
        resultado += f"1. Fuerza eléctrica (F_E = qE):\n"
        resultado += f"   F_E = ({F_E[0]:.3e}, {F_E[1]:.3e}, {F_E[2]:.3e}) N\n"
        resultado += f"   |F_E| = {mag_F_E:.3e} N\n\n"
        
        # Fuerza magnética: F_B = q(v × B)
        # Producto cruz: v × B
        vxB = [
            v[1]*B[2] - v[2]*B[1],
            v[2]*B[0] - v[0]*B[2],
            v[0]*B[1] - v[1]*B[0]
        ]
        F_B = [q * vxB[0], q * vxB[1], q * vxB[2]]
        mag_F_B = math.sqrt(F_B[0]**2 + F_B[1]**2 + F_B[2]**2)
        
        resultado += f"2. Fuerza magnética (F_B = q(v × B)):\n"
        resultado += f"   v × B = ({vxB[0]:.3e}, {vxB[1]:.3e}, {vxB[2]:.3e})\n"
        resultado += f"   F_B = ({F_B[0]:.3e}, {F_B[1]:.3e}, {F_B[2]:.3e}) N\n"
        resultado += f"   |F_B| = {mag_F_B:.3e} N\n\n"
        
        # Fuerza total
        F_total = [F_E[0] + F_B[0], F_E[1] + F_B[1], F_E[2] + F_B[2]]
        mag_F_total = math.sqrt(F_total[0]**2 + F_total[1]**2 + F_total[2]**2)
        
        resultado += f"3. Fuerza total (F = F_E + F_B):\n"
        resultado += f"   F_total = ({F_total[0]:.3e}, {F_total[1]:.3e}, {F_total[2]:.3e}) N\n"
        resultado += f"   |F_total| = {mag_F_total:.3e} N\n\n"
        
        # Aceleración
        a = [F_total[0]/m, F_total[1]/m, F_total[2]/m]
        mag_a = math.sqrt(a[0]**2 + a[1]**2 + a[2]**2)
        
        resultado += "🚀 ACELERACIÓN:\n"
        resultado += f"   a = F/m = ({a[0]:.3e}, {a[1]:.3e}, {a[2]:.3e}) m/s²\n"
        resultado += f"   |a| = {mag_a:.3e} m/s²\n\n"
        
        # Análisis de movimiento
        resultado += "🔄 ANÁLISIS DE MOVIMIENTO:\n"
        
        mag_v = math.sqrt(v[0]**2 + v[1]**2 + v[2]**2)
        mag_E = math.sqrt(E[0]**2 + E[1]**2 + E[2]**2)
        mag_B = math.sqrt(B[0]**2 + B[1]**2 + B[2]**2)
        
        if mag_E > 0 and mag_B == 0:
            resultado += "   Tipo: Movimiento en campo eléctrico únicamente\n"
            resultado += "   Trayectoria: Parabólica (si v no paralelo a E) o rectilínea (si v paralelo a E)\n"
        elif mag_B > 0 and mag_E == 0:
            resultado += "   Tipo: Movimiento en campo magnético únicamente\n"
            resultado += "   Trayectoria: Circular o helicoidal\n"
        elif mag_E > 0 and mag_B > 0:
            resultado += "   Tipo: Movimiento en campos combinados E y B\n"
            resultado += "   Trayectoria: Compleja (combinación de efectos eléctricos y magnéticos)\n"
        
        # Radio de curvatura en campo magnético
        if mag_B > 0:
            resultado += "\n🔵 ANÁLISIS DE CAMPO MAGNÉTICO:\n"
            radio = (m * mag_v) / (abs(q) * mag_B)
            periodo = (2 * math.pi * m) / (abs(q) * mag_B)
            frecuencia = 1 / periodo
            
            resultado += f"   Radio de curvatura: r = mv/(qB) = {radio:.3e} m\n"
            resultado += f"   Período ciclotrónico: T = 2πm/(qB) = {periodo:.3e} s\n"
            resultado += f"   Frecuencia: f = 1/T = {frecuencia:.3e} Hz\n"
            
            # Caso sin campo eléctrico
            if mag_E > 0:
                resultado += f"\n   📌 Si se elimina el campo eléctrico:\n"
                resultado += f"      Radio de trayectoria circular: {radio:.3e} m\n"
                resultado += f"      Movimiento: Circular uniforme\n"
        
        # Energía
        energia_cinetica = 0.5 * m * mag_v**2
        resultado += f"\n⚡ ENERGÍA:\n"
        resultado += f"   Energía cinética: K = ½mv² = {energia_cinetica:.3e} J\n"
        resultado += f"   En eV: {energia_cinetica/self.e:.3e} eV\n"
        
        self.texto_resultados.insert(tk.END, resultado)
    
    def limpiar(self):
        """Limpia las áreas de texto"""
        self.texto_entrada.delete(1.0, tk.END)
        self.texto_resultados.delete(1.0, tk.END)
    
    def cargar_ejemplo(self):
        """Carga un ejemplo diferente"""
        ejemplos = [
            "Un electrón con velocidad v = 1e6 m/s en dirección x entra en un campo magnético B = 0.1 T en dirección z. Calcula el radio de la órbita circular.",
            
            "Una partícula con carga q = 1.6e-19 C y masa m = 1.67e-27 kg tiene velocidad v = (1e5, 1e5, 0) m/s en un campo magnético B = (0, 0, 0.5) T. Analiza el movimiento.",
            
            "Un protón se acelera desde el reposo por un campo eléctrico E = 1000 V/m durante una distancia de 0.01 m. Luego entra en un campo magnético B = 0.02 T perpendicular a su velocidad."
        ]
        
        import random
        ejemplo = random.choice(ejemplos)
        self.texto_entrada.delete(1.0, tk.END)
        self.texto_entrada.insert(tk.END, ejemplo)
    
    def ejecutar(self):
        """Ejecuta la aplicación"""
        self.root.mainloop()

def main():
    """Función principal"""
    try:
        app = CalculadoraLorentzTexto()
        app.ejecutar()
    except Exception as e:
        print(f"Error al ejecutar la aplicación: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
