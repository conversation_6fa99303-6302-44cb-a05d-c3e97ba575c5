"""
Interfaz gráfica con entrada de texto para problemas de fuerza de Lorentz
Permite copiar y pegar problemas en texto y los resuelve automáticamente
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import math

from procesador_texto import ProcesadorTexto
from lorentz_physics import (
    fuerza_lorentz, fuerza_electrica, fuerza_magnetica,
    radio_curvatura_magnetico, periodo_ciclotronica, frecuencia_ciclotronica,
    trabajo_campo_electrico, energia_cinetica, determinar_tipo_trayectoria
)
from vector_utils import calcular_angulo_entre_vectores, radianes_a_grados
from gui_components import PlotFrame

class InterfazTexto:
    """Interfaz principal con entrada de texto"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Calculadora de Fuerza de Lorentz - Entrada por Texto")
        self.root.geometry("1200x800")
        
        self.procesador = ProcesadorTexto()
        self.resultado_actual = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configura la interfaz de usuario"""
        # Frame principal dividido en dos partes
        main_frame = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Panel izquierdo - Entrada de texto
        left_frame = ttk.Frame(main_frame)
        main_frame.add(left_frame, weight=1)
        
        # Panel derecho - Resultados
        right_frame = ttk.Frame(main_frame)
        main_frame.add(right_frame, weight=1)
        
        self.setup_input_panel(left_frame)
        self.setup_results_panel(right_frame)
    
    def setup_input_panel(self, parent):
        """Configura el panel de entrada de texto"""
        # Título
        title_label = ttk.Label(parent, text="Entrada de Problema", 
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 10))
        
        # Instrucciones
        instructions = ttk.Label(parent, 
                                text="Copia y pega aquí el texto del problema de física:",
                                font=('Arial', 10))
        instructions.pack(anchor='w', pady=(0, 5))
        
        # Área de texto para entrada
        self.text_input = scrolledtext.ScrolledText(parent, height=15, width=50, 
                                                   wrap=tk.WORD, font=('Arial', 11))
        self.text_input.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Ejemplo por defecto
        ejemplo_texto = """Un protón con una velocidad de v = (2 × 10⁵) î m/s se mueve en una región donde hay un campo eléctrico E = (3 × 10³) ĵ V/m y un campo magnético B = (0.02) k̂ T. Calcula la fuerza eléctrica, la fuerza magnética, la fuerza total que actúa sobre el protón, la aceleración resultante, determina el tipo de movimiento que describe la partícula y, en el caso de que se elimine el campo eléctrico, calcula el radio de la trayectoria circular que seguiría. Usa los valores q = 1.6 × 10⁻¹⁹ C y m = 1.67 × 10⁻²⁷ kg."""
        
        self.text_input.insert(tk.END, ejemplo_texto)
        
        # Botones
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(button_frame, text="🔍 Analizar Problema", 
                  command=self.analizar_problema).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🧮 Resolver", 
                  command=self.resolver_problema).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🗑️ Limpiar", 
                  command=self.limpiar_todo).pack(side=tk.LEFT, padx=(0, 5))
        
        # Ejemplos predefinidos
        examples_frame = ttk.LabelFrame(parent, text="Ejemplos Rápidos", padding=5)
        examples_frame.pack(fill=tk.X, pady=(10, 0))
        
        ejemplos = [
            ("Ejemplo 1: Protón en campos cruzados", self.cargar_ejemplo_1),
            ("Ejemplo 2: Electrón en campo magnético", self.cargar_ejemplo_2),
            ("Ejemplo 3: Movimiento helicoidal", self.cargar_ejemplo_3)
        ]
        
        for i, (nombre, comando) in enumerate(ejemplos):
            ttk.Button(examples_frame, text=nombre, command=comando).pack(fill=tk.X, pady=1)
    
    def setup_results_panel(self, parent):
        """Configura el panel de resultados"""
        # Título
        title_label = ttk.Label(parent, text="Resultados y Análisis", 
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 10))
        
        # Notebook para organizar resultados
        self.results_notebook = ttk.Notebook(parent)
        self.results_notebook.pack(fill=tk.BOTH, expand=True)
        
        # Pestaña de datos extraídos
        self.data_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.data_frame, text="Datos Extraídos")
        
        self.data_text = scrolledtext.ScrolledText(self.data_frame, height=10, 
                                                  font=('Courier', 10))
        self.data_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Pestaña de cálculos
        self.calc_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.calc_frame, text="Cálculos")
        
        self.calc_text = scrolledtext.ScrolledText(self.calc_frame, height=10, 
                                                  font=('Courier', 10))
        self.calc_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Pestaña de gráficos
        self.plot_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.plot_frame, text="Visualización")
        
        self.plot_widget = PlotFrame(self.plot_frame)
        self.plot_widget.pack(fill=tk.BOTH, expand=True)
    
    def analizar_problema(self):
        """Analiza el texto y extrae los datos"""
        texto = self.text_input.get(1.0, tk.END).strip()
        
        if not texto:
            messagebox.showwarning("Advertencia", "Por favor ingresa un problema para analizar")
            return
        
        # Procesar el texto
        self.resultado_actual = self.procesador.procesar_problema(texto)
        
        if 'error' in self.resultado_actual:
            messagebox.showerror("Error", self.resultado_actual['error'])
            return
        
        # Mostrar datos extraídos
        self.mostrar_datos_extraidos()
        
        messagebox.showinfo("Éxito", "Problema analizado correctamente. Presiona 'Resolver' para ver los cálculos.")
    
    def mostrar_datos_extraidos(self):
        """Muestra los datos extraídos del texto"""
        if not self.resultado_actual:
            return
        
        self.data_text.delete(1.0, tk.END)
        
        datos = self.resultado_actual['datos_extraidos']
        particula = self.resultado_actual['particula']
        campo = self.resultado_actual['campo']
        
        output = "=== DATOS EXTRAÍDOS DEL PROBLEMA ===\n\n"
        
        output += f"Tipo de partícula: {datos['tipo_particula'].title()}\n"
        output += f"Carga: {particula.carga:.3e} C\n"
        output += f"Masa: {particula.masa:.3e} kg\n"
        output += f"Velocidad inicial: {particula.velocidad} m/s\n"
        output += f"  Magnitud: {particula.velocidad.magnitude():.3e} m/s\n\n"
        
        output += f"Campo eléctrico E: {campo.E} V/m\n"
        output += f"  Magnitud: {campo.E.magnitude():.3e} V/m\n\n"
        
        output += f"Campo magnético B: {campo.B} T\n"
        output += f"  Magnitud: {campo.B.magnitude():.3e} T\n\n"
        
        output += "Preguntas identificadas:\n"
        for i, pregunta in enumerate(self.resultado_actual['preguntas'], 1):
            output += f"  {i}. {pregunta.replace('_', ' ').title()}\n"
        
        output += "\n" + "="*50 + "\n"
        output += "Presiona 'Resolver' para ver los cálculos completos"
        
        self.data_text.insert(tk.END, output)
    
    def resolver_problema(self):
        """Resuelve el problema completamente"""
        if not self.resultado_actual:
            messagebox.showwarning("Advertencia", "Primero analiza el problema")
            return
        
        particula = self.resultado_actual['particula']
        campo = self.resultado_actual['campo']
        preguntas = self.resultado_actual['preguntas']
        
        # Realizar todos los cálculos
        self.calc_text.delete(1.0, tk.END)
        
        output = "=== SOLUCIÓN COMPLETA ===\n\n"
        
        # Cálculos básicos de fuerza
        fuerza_E = fuerza_electrica(particula, campo.E)
        fuerza_B = fuerza_magnetica(particula, campo.B)
        fuerza_total = fuerza_lorentz(particula, campo)
        
        output += "1. FUERZAS:\n"
        output += f"   Fuerza eléctrica (F_E = qE):\n"
        output += f"     F_E = {fuerza_E} N\n"
        output += f"     |F_E| = {fuerza_E.magnitude():.3e} N\n\n"
        
        output += f"   Fuerza magnética (F_B = q(v×B)):\n"
        output += f"     F_B = {fuerza_B} N\n"
        output += f"     |F_B| = {fuerza_B.magnitude():.3e} N\n\n"
        
        output += f"   Fuerza total (F = F_E + F_B):\n"
        output += f"     F_total = {fuerza_total} N\n"
        output += f"     |F_total| = {fuerza_total.magnitude():.3e} N\n\n"
        
        # Aceleración
        aceleracion = fuerza_total * (1/particula.masa)
        output += "2. ACELERACIÓN:\n"
        output += f"   a = F/m = {aceleracion} m/s²\n"
        output += f"   |a| = {aceleracion.magnitude():.3e} m/s²\n\n"
        
        # Tipo de movimiento
        tipo_movimiento = determinar_tipo_trayectoria(particula, campo)
        output += "3. TIPO DE MOVIMIENTO:\n"
        output += f"   {tipo_movimiento}\n\n"
        
        # Análisis específico según el tipo
        if campo.B.magnitude() > 0:
            radio = radio_curvatura_magnetico(particula, campo.B)
            periodo = periodo_ciclotronica(particula, campo.B)
            frecuencia = frecuencia_ciclotronica(particula, campo.B)
            
            output += "4. ANÁLISIS DE CAMPO MAGNÉTICO:\n"
            output += f"   Radio de curvatura: r = mv/(qB) = {radio:.3e} m\n"
            output += f"   Período ciclotrónico: T = 2πm/(qB) = {periodo:.3e} s\n"
            output += f"   Frecuencia: f = 1/T = {frecuencia:.3e} Hz\n\n"
        
        # Energía
        energia_cin = energia_cinetica(particula)
        output += "5. ENERGÍA:\n"
        output += f"   Energía cinética inicial: K = ½mv² = {energia_cin:.3e} J\n"
        output += f"   En eV: {energia_cin/1.602176634e-19:.3e} eV\n\n"
        
        # Ángulos
        if particula.velocidad.magnitude() > 0 and campo.E.magnitude() > 0:
            angulo_vE = calcular_angulo_entre_vectores(particula.velocidad, campo.E)
            output += "6. ÁNGULOS:\n"
            output += f"   Ángulo v-E: {radianes_a_grados(angulo_vE):.1f}°\n"
        
        if particula.velocidad.magnitude() > 0 and campo.B.magnitude() > 0:
            angulo_vB = calcular_angulo_entre_vectores(particula.velocidad, campo.B)
            output += f"   Ángulo v-B: {radianes_a_grados(angulo_vB):.1f}°\n\n"
        
        # Caso especial: sin campo eléctrico
        if campo.E.magnitude() > 0:
            output += "7. CASO SIN CAMPO ELÉCTRICO:\n"
            output += "   Si se elimina el campo eléctrico:\n"
            radio_sin_E = radio_curvatura_magnetico(particula, campo.B)
            output += f"   Radio de trayectoria circular: {radio_sin_E:.3e} m\n"
            output += f"   Movimiento: Circular uniforme\n\n"
        
        self.calc_text.insert(tk.END, output)
        
        # Crear gráfico
        self.crear_grafico()
        
        # Cambiar a pestaña de cálculos
        self.results_notebook.select(1)
    
    def crear_grafico(self):
        """Crea visualización de vectores"""
        if not self.resultado_actual:
            return
        
        particula = self.resultado_actual['particula']
        campo = self.resultado_actual['campo']
        
        # Calcular fuerzas para el gráfico
        fuerza_E = fuerza_electrica(particula, campo.E)
        fuerza_B = fuerza_magnetica(particula, campo.B)
        fuerza_total = fuerza_lorentz(particula, campo)
        
        # Preparar datos para el gráfico
        vectors_data = []
        
        if particula.velocidad.magnitude() > 0:
            vectors_data.append((particula.velocidad.to_array(), "Velocidad", "blue"))
        
        if campo.E.magnitude() > 0:
            vectors_data.append((campo.E.to_array(), "Campo E", "red"))
        
        if campo.B.magnitude() > 0:
            vectors_data.append((campo.B.to_array(), "Campo B", "green"))
        
        if fuerza_E.magnitude() > 0:
            vectors_data.append((fuerza_E.to_array(), "Fuerza Eléctrica", "orange"))
        
        if fuerza_B.magnitude() > 0:
            vectors_data.append((fuerza_B.to_array(), "Fuerza Magnética", "purple"))
        
        if fuerza_total.magnitude() > 0:
            vectors_data.append((fuerza_total.to_array(), "Fuerza Total", "black"))
        
        self.plot_widget.plot_vectors(vectors_data)
    
    def limpiar_todo(self):
        """Limpia toda la interfaz"""
        self.text_input.delete(1.0, tk.END)
        self.data_text.delete(1.0, tk.END)
        self.calc_text.delete(1.0, tk.END)
        self.plot_widget.clear_plot()
        self.resultado_actual = None
    
    def cargar_ejemplo_1(self):
        """Carga ejemplo 1: Protón en campos cruzados"""
        ejemplo = """Un protón con velocidad v = (2×10⁵) î m/s se mueve en campos E = (3×10³) ĵ V/m y B = (0.02) k̂ T. Calcula todas las fuerzas y el tipo de movimiento."""
        self.text_input.delete(1.0, tk.END)
        self.text_input.insert(tk.END, ejemplo)
    
    def cargar_ejemplo_2(self):
        """Carga ejemplo 2: Electrón en campo magnético"""
        ejemplo = """Un electrón con velocidad de 1×10⁶ m/s en dirección x entra en un campo magnético B = 0.1 T en dirección z. Determina el radio de la órbita circular y el período."""
        self.text_input.delete(1.0, tk.END)
        self.text_input.insert(tk.END, ejemplo)
    
    def cargar_ejemplo_3(self):
        """Carga ejemplo 3: Movimiento helicoidal"""
        ejemplo = """Una partícula con carga q = 1.6×10⁻¹⁹ C y masa m = 1.67×10⁻²⁷ kg tiene velocidad v = (1×10⁵, 1×10⁵, 2×10⁵) m/s en un campo magnético B = (0, 0, 0.5) T. Analiza el movimiento helicoidal."""
        self.text_input.delete(1.0, tk.END)
        self.text_input.insert(tk.END, ejemplo)

def main():
    """Función principal"""
    root = tk.Tk()
    app = InterfazTexto(root)
    root.mainloop()

if __name__ == "__main__":
    main()
