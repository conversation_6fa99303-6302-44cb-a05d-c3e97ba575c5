import bpy
import math
import random
import bmesh

# Limpiar escena
if bpy.context.mode != 'OBJECT':
    bpy.ops.object.mode_set(mode='OBJECT')

# Seleccionar todos los objetos
for obj in bpy.context.scene.objects:
    obj.select_set(True)

# Eliminar todos los objetos
bpy.ops.object.delete(use_global=False)

# Función para crear terreno con montañas usando displacement
def crear_terreno_montañoso():
    # Crear plano base grande
    bpy.ops.mesh.primitive_plane_add(size=200, enter_editmode=False, location=(0, 0, 0))
    terreno = bpy.context.active_object
    terreno.name = "Terreno_Base"
    
    # Añadir subdivisiones para detalle
    bpy.context.view_layer.objects.active = terreno
    terreno.select_set(True)
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.subdivide(number_cuts=50)
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Crear material para terreno
    mat_terreno = bpy.data.materials.new(name="Material_Terreno")
    mat_terreno.use_nodes = True
    nodes = mat_terreno.node_tree.nodes
    links = mat_terreno.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos para terreno realista
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    noise1 = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    
    # Configurar texturas
    noise1.inputs['Scale'].default_value = 5.0
    noise1.inputs['Detail'].default_value = 15.0
    noise1.inputs['Roughness'].default_value = 0.5
    
    # Configurar ColorRamp para diferentes alturas
    color_ramp.color_ramp.elements[0].position = 0.0
    color_ramp.color_ramp.elements[0].color = (0.2, 0.4, 0.1, 1.0)  # Verde oscuro (valle)
    color_ramp.color_ramp.elements[1].position = 1.0
    color_ramp.color_ramp.elements[1].color = (0.8, 0.8, 0.9, 1.0)  # Gris claro (montaña)
    
    # Añadir puntos intermedios
    color_ramp.color_ramp.elements.new(0.3)
    color_ramp.color_ramp.elements[1].color = (0.4, 0.6, 0.2, 1.0)  # Verde medio
    color_ramp.color_ramp.elements.new(0.7)
    color_ramp.color_ramp.elements[2].color = (0.5, 0.4, 0.3, 1.0)  # Marrón (tierra)
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise1.inputs['Vector'])
    links.new(noise1.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Aplicar material
    terreno.data.materials.append(mat_terreno)
    
    # Añadir modificador de displacement para crear montañas
    displacement_mod = terreno.modifiers.new(name="Displacement", type='DISPLACE')
    
    # Crear textura para displacement
    tex_displacement = bpy.data.textures.new(name="Displacement_Texture", type='CLOUDS')
    tex_displacement.noise_scale = 0.5
    tex_displacement.noise_depth = 8
    tex_displacement.nabla = 0.025
    
    displacement_mod.texture = tex_displacement
    displacement_mod.strength = 15  # Altura de las montañas
    displacement_mod.mid_level = 0.5
    
    return terreno

# Función para crear río serpenteante
def crear_rio():
    # Crear curva para el río
    bpy.ops.curve.primitive_bezier_curve_add(enter_editmode=False, location=(0, 0, 0.1))
    curva_rio = bpy.context.active_object
    curva_rio.name = "Curva_Rio"
    
    # Modificar la curva para hacerla serpenteante
    bpy.context.view_layer.objects.active = curva_rio
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Añadir más puntos a la curva
    for i in range(10):
        bpy.ops.curve.extrude_move(TRANSFORM_OT_translate={"value": (random.uniform(-5, 5), 10, 0)})
    
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Configurar propiedades de la curva
    curva_rio.data.bevel_depth = 2.0  # Ancho del río
    curva_rio.data.bevel_resolution = 4
    curva_rio.data.resolution_u = 20
    
    # Convertir a mesh
    bpy.ops.object.convert(target='MESH')
    rio = bpy.context.active_object
    rio.name = "Rio"
    
    # Material para agua
    mat_agua = bpy.data.materials.new(name="Material_Agua")
    mat_agua.use_nodes = True
    nodes = mat_agua.node_tree.nodes
    links = mat_agua.node_tree.links
    
    # Limpiar nodos existentes
    for node in nodes:
        nodes.remove(node)
    
    # Crear nodos para agua realista
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    noise = nodes.new(type='ShaderNodeTexNoise')
    tex_coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    
    # Configurar propiedades del agua
    principled.inputs['Base Color'].default_value = (0.1, 0.3, 0.6, 1.0)  # Azul agua
    principled.inputs['Metallic'].default_value = 0.0
    principled.inputs['Roughness'].default_value = 0.1
    principled.inputs['Transmission'].default_value = 0.8
    principled.inputs['Alpha'].default_value = 0.8
    
    # Configurar ruido para ondas
    noise.inputs['Scale'].default_value = 20.0
    noise.inputs['Detail'].default_value = 5.0
    
    # Conectar nodos
    links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], noise.inputs['Vector'])
    links.new(noise.outputs['Fac'], principled.inputs['Normal'])
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Configurar material como transparente
    mat_agua.blend_method = 'BLEND'
    
    # Aplicar material
    rio.data.materials.append(mat_agua)
    
    return rio

# Función para crear árboles variados
def crear_arbol_detallado(location, tipo="roble"):
    altura_tronco = random.uniform(3, 6)
    
    # Tronco principal
    bpy.ops.mesh.primitive_cylinder_add(radius=random.uniform(0.3, 0.5), 
                                      depth=altura_tronco,
                                      enter_editmode=False, 
                                      location=(location[0], location[1], altura_tronco/2))
    tronco = bpy.context.active_object
    tronco.name = f"Tronco_{tipo}_{location[0]}_{location[1]}"
    
    # Hacer el tronco más irregular
    bpy.context.view_layer.objects.active = tronco
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.subdivide(number_cuts=3)
    bpy.ops.transform.resize(value=(1, 1, random.uniform(0.8, 1.2)))
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Material para tronco
    mat_tronco = bpy.data.materials.new(name=f"Material_Tronco_{tipo}")
    mat_tronco.use_nodes = True
    nodes = mat_tronco.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.3, 0.2, 0.1, 1.0)  # Marrón
    bsdf.inputs[7].default_value = 0.9  # Rugosidad alta
    tronco.data.materials.append(mat_tronco)
    
    # Copa del árbol según el tipo
    if tipo == "roble":
        # Copa redonda y densa
        bpy.ops.mesh.primitive_ico_sphere_add(radius=random.uniform(2, 3),
                                            enter_editmode=False,
                                            location=(location[0], location[1], altura_tronco + 2))
        copa = bpy.context.active_object
        copa.scale = (1, 1, 0.8)  # Aplanar un poco
    elif tipo == "pino":
        # Copa cónica
        bpy.ops.mesh.primitive_cone_add(radius1=random.uniform(1.5, 2.5),
                                      depth=random.uniform(4, 6),
                                      enter_editmode=False,
                                      location=(location[0], location[1], altura_tronco + 2))
        copa = bpy.context.active_object
    else:  # sauce
        # Copa alargada hacia abajo
        bpy.ops.mesh.primitive_ico_sphere_add(radius=random.uniform(2, 3),
                                            enter_editmode=False,
                                            location=(location[0], location[1], altura_tronco + 1))
        copa = bpy.context.active_object
        copa.scale = (1.2, 1.2, 1.5)  # Alargar verticalmente
    
    copa.name = f"Copa_{tipo}_{location[0]}_{location[1]}"
    
    # Material para copa
    mat_copa = bpy.data.materials.new(name=f"Material_Copa_{tipo}")
    mat_copa.use_nodes = True
    nodes = mat_copa.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    
    if tipo == "pino":
        bsdf.inputs[0].default_value = (0.1, 0.3, 0.1, 1.0)  # Verde oscuro
    else:
        verde = random.uniform(0.2, 0.5)
        bsdf.inputs[0].default_value = (0.1, verde, 0.1, 1.0)  # Verde variado
    
    bsdf.inputs[7].default_value = 0.8  # Rugosidad
    copa.data.materials.append(mat_copa)
    
    return tronco, copa

# Función para crear rocas
def crear_roca(location, tamaño=None):
    if tamaño is None:
        tamaño = random.uniform(0.5, 2.0)
    
    # Crear esfera base
    bpy.ops.mesh.primitive_ico_sphere_add(radius=tamaño,
                                        enter_editmode=False,
                                        location=location)
    roca = bpy.context.active_object
    roca.name = f"Roca_{location[0]}_{location[1]}"
    
    # Deformar para hacer más irregular
    bpy.context.view_layer.objects.active = roca
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.subdivide(number_cuts=2)
    bpy.ops.transform.resize(value=(random.uniform(0.7, 1.3), 
                                  random.uniform(0.7, 1.3), 
                                  random.uniform(0.5, 1.0)))
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Material para roca
    mat_roca = bpy.data.materials.new(name=f"Material_Roca_{location[0]}_{location[1]}")
    mat_roca.use_nodes = True
    nodes = mat_roca.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    gris = random.uniform(0.3, 0.6)
    bsdf.inputs[0].default_value = (gris, gris, gris * 0.9, 1.0)  # Gris variado
    bsdf.inputs[7].default_value = 0.9  # Rugosidad alta
    roca.data.materials.append(mat_roca)
    
    return roca

# Crear elementos del paisaje
terreno = crear_terreno_montañoso()
rio = crear_rio()

# Crear bosque variado
arboles = []
tipos_arboles = ["roble", "pino", "sauce"]

# Bosque denso en las laderas
for _ in range(150):
    x = random.uniform(-80, 80)
    y = random.uniform(-80, 80)
    # Evitar el área del río
    if abs(y) > 10 or abs(x) > 15:
        tipo = random.choice(tipos_arboles)
        arbol = crear_arbol_detallado((x, y, 0), tipo)
        arboles.append(arbol)

# Árboles cerca del río (principalmente sauces)
for _ in range(30):
    x = random.uniform(-20, 20)
    y = random.uniform(-15, 15)
    if 5 < abs(y) < 12:  # Cerca pero no en el río
        arbol = crear_arbol_detallado((x, y, 0), "sauce")
        arboles.append(arbol)

# Crear rocas dispersas
rocas = []
for _ in range(50):
    x = random.uniform(-90, 90)
    y = random.uniform(-90, 90)
    # Más rocas en las montañas
    if random.random() > 0.3:
        roca = crear_roca((x, y, random.uniform(0, 2)))
        rocas.append(roca)

# Crear flores y arbustos
def crear_arbusto(location):
    bpy.ops.mesh.primitive_ico_sphere_add(radius=random.uniform(0.3, 0.8),
                                        enter_editmode=False,
                                        location=location)
    arbusto = bpy.context.active_object
    arbusto.name = f"Arbusto_{location[0]}_{location[1]}"
    arbusto.scale = (1, 1, 0.6)  # Aplanar

    # Material para arbusto
    mat_arbusto = bpy.data.materials.new(name=f"Material_Arbusto_{location[0]}_{location[1]}")
    mat_arbusto.use_nodes = True
    nodes = mat_arbusto.node_tree.nodes
    bsdf = nodes.get("Principled BSDF")
    bsdf.inputs[0].default_value = (0.2, 0.4, 0.1, 1.0)  # Verde arbusto
    bsdf.inputs[7].default_value = 0.7
    arbusto.data.materials.append(mat_arbusto)

    return arbusto

# Crear arbustos
arbustos = []
for _ in range(80):
    x = random.uniform(-70, 70)
    y = random.uniform(-70, 70)
    if abs(y) > 8:  # Evitar el río
        arbusto = crear_arbusto((x, y, 0))
        arbustos.append(arbusto)

# Añadir iluminación natural
# Sol principal
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 50))
sol = bpy.context.active_object
sol.name = "Sol"
sol.data.energy = 4
sol.rotation_euler = (math.radians(45), 0, math.radians(30))
sol.data.color = (1.0, 0.95, 0.8)  # Luz cálida

# Luz de relleno suave
bpy.ops.object.light_add(type='SUN', radius=1, location=(0, 0, 50))
luz_relleno = bpy.context.active_object
luz_relleno.name = "Luz_Relleno"
luz_relleno.data.energy = 1.5
luz_relleno.rotation_euler = (math.radians(45), 0, math.radians(210))
luz_relleno.data.color = (0.7, 0.8, 1.0)  # Luz azulada suave

# Añadir cámara con vista panorámica
bpy.ops.object.camera_add(enter_editmode=False, align='VIEW',
                        location=(50, -50, 30),
                        rotation=(math.radians(60), 0, math.radians(45)))
camara = bpy.context.active_object
camara.name = "Camara_Paisaje"
bpy.context.scene.camera = camara

# Configurar fondo de cielo natural
world = bpy.context.scene.world
world.use_nodes = True
nodes = world.node_tree.nodes
links = world.node_tree.links

# Limpiar nodos existentes
for node in nodes:
    nodes.remove(node)

# Crear nodos para cielo realista
background = nodes.new(type='ShaderNodeBackground')
output = nodes.new(type='ShaderNodeOutputWorld')
sky_texture = nodes.new(type='ShaderNodeTexSky')

# Configurar cielo
sky_texture.sky_type = 'PREETHAM'
sky_texture.sun_elevation = math.radians(45)
sky_texture.sun_rotation = math.radians(30)
sky_texture.turbidity = 2.0
sky_texture.ground_albedo = 0.3

# Conectar nodos
links.new(sky_texture.outputs['Color'], background.inputs['Color'])
links.new(background.outputs['Background'], output.inputs['Surface'])

print("¡Paisaje natural con montañas y río creado con éxito!")
print("Incluye: terreno montañoso, río serpenteante, bosque variado, rocas y vegetación")
print("Para renderizar, presiona F12 o usa el menú Render > Render Image")
