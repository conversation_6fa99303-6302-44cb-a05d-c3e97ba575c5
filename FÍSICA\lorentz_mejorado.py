#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CALCULADORA DE FUERZA DE LORENTZ - VERSIÓN MEJORADA
Precisión numérica, validación de unidades y cálculos vectoriales correctos
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox
import re
import math
from decimal import Decimal, getcontext

# Configurar precisión decimal
getcontext().prec = 50

class Vector3D:
    """Clase para vectores 3D con precisión mejorada"""
    
    def __init__(self, x=0.0, y=0.0, z=0.0):
        # Usar float64 para máxima precisión
        self.x = float(x)
        self.y = float(y)
        self.z = float(z)
        
        # Validar que no hay NaN o infinitos
        if not all(math.isfinite(val) for val in [self.x, self.y, self.z]):
            raise ValueError("Componentes vectoriales deben ser números finitos")
    
    def __str__(self):
        return f"({self.x:.6e}, {self.y:.6e}, {self.z:.6e})"
    
    def __add__(self, other):
        return Vector3D(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other):
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar):
        return Vector3D(self.x * scalar, self.y * scalar, self.z * scalar)
    
    def __rmul__(self, scalar):
        return self.__mul__(scalar)
    
    def magnitude(self):
        """Magnitud con precisión mejorada"""
        return math.sqrt(self.x*self.x + self.y*self.y + self.z*self.z)
    
    def normalize(self):
        """Vector unitario"""
        mag = self.magnitude()
        if mag < 1e-15:  # Evitar división por cero
            return Vector3D(0, 0, 0)
        return Vector3D(self.x/mag, self.y/mag, self.z/mag)
    
    def dot(self, other):
        """Producto punto"""
        return self.x * other.x + self.y * other.y + self.z * other.z
    
    def cross(self, other):
        """Producto cruz con precisión mejorada"""
        x = self.y * other.z - self.z * other.y
        y = self.z * other.x - self.x * other.z
        z = self.x * other.y - self.y * other.x
        return Vector3D(x, y, z)
    
    def is_parallel(self, other, tolerance=1e-12):
        """Verifica si dos vectores son paralelos"""
        cross_product = self.cross(other)
        return cross_product.magnitude() < tolerance
    
    def is_perpendicular(self, other, tolerance=1e-12):
        """Verifica si dos vectores son perpendiculares"""
        return abs(self.dot(other)) < tolerance
    
    def angle_with(self, other):
        """Ángulo entre vectores en radianes"""
        mag_product = self.magnitude() * other.magnitude()
        if mag_product < 1e-15:
            return 0.0
        
        cos_angle = self.dot(other) / mag_product
        # Limitar para evitar errores numéricos
        cos_angle = max(-1.0, min(1.0, cos_angle))
        return math.acos(cos_angle)

class ValidadorUnidades:
    """Validador de unidades del Sistema Internacional"""
    
    @staticmethod
    def validar_carga(valor, unidad="C"):
        """Valida y convierte carga a Coulombs"""
        conversiones = {
            "C": 1.0,
            "e": 1.602176634e-19,
            "mC": 1e-3,
            "μC": 1e-6,
            "nC": 1e-9
        }
        
        if unidad not in conversiones:
            raise ValueError(f"Unidad de carga '{unidad}' no reconocida")
        
        return valor * conversiones[unidad]
    
    @staticmethod
    def validar_masa(valor, unidad="kg"):
        """Valida y convierte masa a kilogramos"""
        conversiones = {
            "kg": 1.0,
            "g": 1e-3,
            "u": 1.66053906660e-27,  # Unidad de masa atómica
            "MeV/c²": 1.78266192e-30
        }
        
        if unidad not in conversiones:
            raise ValueError(f"Unidad de masa '{unidad}' no reconocida")
        
        return valor * conversiones[unidad]
    
    @staticmethod
    def validar_velocidad(valor, unidad="m/s"):
        """Valida y convierte velocidad a m/s"""
        conversiones = {
            "m/s": 1.0,
            "km/s": 1000.0,
            "km/h": 1.0/3.6,
            "c": 299792458.0
        }
        
        if unidad not in conversiones:
            raise ValueError(f"Unidad de velocidad '{unidad}' no reconocida")
        
        return valor * conversiones[unidad]
    
    @staticmethod
    def validar_campo_electrico(valor, unidad="V/m"):
        """Valida campo eléctrico en V/m"""
        conversiones = {
            "V/m": 1.0,
            "kV/m": 1000.0,
            "N/C": 1.0  # Equivalente a V/m
        }
        
        if unidad not in conversiones:
            raise ValueError(f"Unidad de campo eléctrico '{unidad}' no reconocida")
        
        return valor * conversiones[unidad]
    
    @staticmethod
    def validar_campo_magnetico(valor, unidad="T"):
        """Valida campo magnético en Tesla"""
        conversiones = {
            "T": 1.0,
            "mT": 1e-3,
            "μT": 1e-6,
            "G": 1e-4,  # Gauss
            "mG": 1e-7
        }
        
        if unidad not in conversiones:
            raise ValueError(f"Unidad de campo magnético '{unidad}' no reconocida")
        
        return valor * conversiones[unidad]

class CalculadoraLorentzMejorada:
    def __init__(self):
        # Constantes físicas con máxima precisión
        self.CONSTANTES = {
            'e': 1.602176634e-19,      # Carga elemental (C)
            'me': 9.1093837015e-31,    # Masa electrón (kg)
            'mp': 1.67262192369e-27,   # Masa protón (kg)
            'c': 299792458.0,          # Velocidad de la luz (m/s)
            'epsilon0': 8.8541878128e-12,  # Permitividad del vacío
            'mu0': 1.25663706212e-6    # Permeabilidad del vacío
        }
        
        self.validador = ValidadorUnidades()
        self.crear_interfaz()
    
    def crear_interfaz(self):
        self.ventana = tk.Tk()
        self.ventana.title("⚡ Calculadora de Fuerza de Lorentz - VERSIÓN MEJORADA ⚡")
        self.ventana.geometry("1000x800")
        self.ventana.configure(bg='#f0f8ff')
        
        # Título
        titulo = tk.Label(self.ventana, 
                         text="🔬 CALCULADORA DE FUERZA DE LORENTZ - PRECISIÓN MEJORADA 🔬",
                         font=('Arial', 16, 'bold'), 
                         bg='#f0f8ff', fg='#000080')
        titulo.pack(pady=10)
        
        # Advertencia de precisión
        advertencia = tk.Label(self.ventana,
                             text="⚠️ VERSIÓN MEJORADA: Validación de unidades SI, precisión numérica y cálculos vectoriales correctos",
                             font=('Arial', 10), bg='#ffffcc', fg='#cc6600',
                             relief=tk.RAISED, bd=2)
        advertencia.pack(pady=5, padx=20, fill=tk.X)
        
        # Área de entrada
        tk.Label(self.ventana, text="📝 Problema de física:", 
                font=('Arial', 12, 'bold'), bg='#f0f8ff').pack(anchor='w', padx=20, pady=(10,5))
        
        self.entrada = scrolledtext.ScrolledText(self.ventana, height=6, width=110,
                                               font=('Arial', 11), wrap=tk.WORD)
        self.entrada.pack(padx=20, pady=5, fill=tk.X)
        
        # Ejemplo mejorado
        ejemplo = """Un protón con velocidad v = (2.00 × 10⁵) î m/s se mueve en campos E = (3.00 × 10³) ĵ V/m y B = (2.00 × 10⁻²) k̂ T. Calcula con precisión: fuerza eléctrica, fuerza magnética, fuerza total, aceleración, tipo de trayectoria y radio de curvatura sin campo eléctrico. Masa protón: 1.673 × 10⁻²⁷ kg, carga: 1.602 × 10⁻¹⁹ C."""
        self.entrada.insert(tk.END, ejemplo)
        
        # Botones
        frame_botones = tk.Frame(self.ventana, bg='#f0f8ff')
        frame_botones.pack(pady=10)
        
        tk.Button(frame_botones, text="🧮 RESOLVER CON PRECISIÓN",
                 command=self.resolver_con_precision, font=('Arial', 12, 'bold'),
                 bg='#228b22', fg='white', padx=20, pady=5).pack(side=tk.LEFT, padx=5)
        
        tk.Button(frame_botones, text="🔍 VALIDAR UNIDADES",
                 command=self.validar_unidades_texto, font=('Arial', 11),
                 bg='#4169e1', fg='white', padx=15, pady=5).pack(side=tk.LEFT, padx=5)
        
        tk.Button(frame_botones, text="🗑️ LIMPIAR",
                 command=self.limpiar, font=('Arial', 11),
                 bg='#dc143c', fg='white', padx=15, pady=5).pack(side=tk.LEFT, padx=5)
        
        # Área de resultados
        tk.Label(self.ventana, text="📊 RESULTADOS CON VALIDACIÓN:",
                font=('Arial', 12, 'bold'), bg='#f0f8ff').pack(anchor='w', padx=20, pady=(15,5))
        
        self.resultados = scrolledtext.ScrolledText(self.ventana, height=18, width=110,
                                                  font=('Courier', 9), wrap=tk.WORD,
                                                  bg='#fffacd')
        self.resultados.pack(padx=20, pady=5, fill=tk.BOTH, expand=True)
    
    def extraer_datos_mejorados(self, texto):
        """Extracción de datos con validación mejorada"""
        # Normalizar notación científica
        texto = re.sub(r'(\d+(?:\.\d+)?)\s*×\s*10\^?([+-]?\d+)', r'\1e\2', texto)
        texto = re.sub(r'(\d+(?:\.\d+)?)\s*\*\s*10\^?([+-]?\d+)', r'\1e\2', texto)
        
        datos = {}
        
        # Identificar partícula con validación
        texto_lower = texto.lower()
        if 'protón' in texto_lower or 'proton' in texto_lower:
            datos['tipo'] = 'Protón'
            datos['q'] = self.CONSTANTES['e']
            datos['m'] = self.CONSTANTES['mp']
        elif 'electrón' in texto_lower or 'electron' in texto_lower:
            datos['tipo'] = 'Electrón'
            datos['q'] = -self.CONSTANTES['e']
            datos['m'] = self.CONSTANTES['me']
        else:
            datos['tipo'] = 'Partícula personalizada'
            datos['q'] = self.CONSTANTES['e']
            datos['m'] = self.CONSTANTES['mp']
        
        # Extraer vectores con validación
        try:
            datos['v'] = self.extraer_vector_validado(texto, 'v', 'velocidad')
            datos['E'] = self.extraer_vector_validado(texto, 'E', 'campo_electrico')
            datos['B'] = self.extraer_vector_validado(texto, 'B', 'campo_magnetico')
        except Exception as e:
            raise ValueError(f"Error en extracción de vectores: {e}")
        
        # Validar que los vectores tienen sentido físico
        self.validar_vectores_fisicos(datos)
        
        return datos
    
    def extraer_vector_validado(self, texto, variable, tipo):
        """Extrae vector con validación de componentes"""
        # Patrones mejorados para mayor precisión
        patron_i = rf'{variable}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[îi]'
        patron_j = rf'{variable}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[ĵj]'
        patron_k = rf'{variable}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[k̂k]'
        
        x = self.buscar_numero_preciso(texto, patron_i)
        y = self.buscar_numero_preciso(texto, patron_j)
        z = self.buscar_numero_preciso(texto, patron_k)
        
        # Formato paréntesis como respaldo
        if x == 0 and y == 0 and z == 0:
            patron_parentesis = rf'{variable}[^=]*=\s*\(([^)]+)\)'
            match = re.search(patron_parentesis, texto, re.IGNORECASE)
            if match:
                componentes = [comp.strip() for comp in match.group(1).split(',')]
                if len(componentes) >= 3:
                    try:
                        x = float(re.sub(r'[^\d.e+-]', '', componentes[0]))
                        y = float(re.sub(r'[^\d.e+-]', '', componentes[1]))
                        z = float(re.sub(r'[^\d.e+-]', '', componentes[2]))
                    except ValueError:
                        pass
        
        # Crear vector con validación
        vector = Vector3D(x, y, z)
        
        # Validar rangos físicos razonables
        self.validar_rango_fisico(vector, tipo)
        
        return vector
    
    def buscar_numero_preciso(self, texto, patron):
        """Búsqueda de números con precisión mejorada"""
        match = re.search(patron, texto, re.IGNORECASE)
        if match:
            try:
                # Usar Decimal para máxima precisión en la conversión
                numero_str = match.group(1).replace(' ', '')
                return float(Decimal(numero_str))
            except:
                return 0.0
        return 0.0
    
    def validar_vectores_fisicos(self, datos):
        """Valida que los vectores tengan sentido físico"""
        v, E, B = datos['v'], datos['E'], datos['B']
        
        # Verificar que la velocidad no exceda c
        if v.magnitude() > self.CONSTANTES['c']:
            raise ValueError(f"Velocidad {v.magnitude():.2e} m/s excede la velocidad de la luz")
        
        # Verificar campos razonables
        if E.magnitude() > 1e12:  # 1 TV/m es extremo
            raise ValueError(f"Campo eléctrico {E.magnitude():.2e} V/m es físicamente irrazonable")
        
        if B.magnitude() > 100:  # 100 T es muy alto
            raise ValueError(f"Campo magnético {B.magnitude():.2e} T es físicamente irrazonable")
    
    def validar_rango_fisico(self, vector, tipo):
        """Valida rangos físicos razonables"""
        mag = vector.magnitude()
        
        rangos = {
            'velocidad': (0, self.CONSTANTES['c']),
            'campo_electrico': (0, 1e12),
            'campo_magnetico': (0, 100)
        }
        
        if tipo in rangos:
            min_val, max_val = rangos[tipo]
            if not (min_val <= mag <= max_val):
                raise ValueError(f"{tipo}: {mag:.2e} fuera del rango físico razonable")
    
    def calcular_fuerzas_precisas(self, datos):
        """Cálculo de fuerzas con precisión mejorada"""
        q, m = datos['q'], datos['m']
        v, E, B = datos['v'], datos['E'], datos['B']
        
        # Fuerza eléctrica: F_E = qE (producto escalar-vector)
        F_E = E * q
        
        # Fuerza magnética: F_B = q(v × B) (producto cruz correcto)
        v_cross_B = v.cross(B)
        F_B = v_cross_B * q
        
        # Fuerza total: superposición lineal
        F_total = F_E + F_B
        
        # Validar que no hay componentes artificiales
        self.validar_componentes_artificiales(F_E, E, "eléctrica")
        self.validar_componentes_artificiales(F_B, v_cross_B, "magnética")
        
        return F_E, F_B, F_total
    
    def validar_componentes_artificiales(self, fuerza, campo_base, tipo):
        """Verifica que no se generen componentes artificiales"""
        tolerance = 1e-15
        
        # Si el campo base tiene componente cero, la fuerza también debe tenerla
        if abs(campo_base.x) < tolerance and abs(fuerza.x) > tolerance:
            raise ValueError(f"Componente X artificial en fuerza {tipo}")
        if abs(campo_base.y) < tolerance and abs(fuerza.y) > tolerance:
            raise ValueError(f"Componente Y artificial en fuerza {tipo}")
        if abs(campo_base.z) < tolerance and abs(fuerza.z) > tolerance:
            raise ValueError(f"Componente Z artificial en fuerza {tipo}")
    
    def analizar_trayectoria_precisa(self, v, B):
        """Análisis preciso del tipo de trayectoria"""
        if B.magnitude() < 1e-15:
            return "Rectilínea (sin campo magnético)"
        
        # Descomponer velocidad respecto a B
        B_unit = B.normalize()
        v_paralelo_escalar = v.dot(B_unit)
        v_paralelo = B_unit * v_paralelo_escalar
        v_perpendicular = v - v_paralelo
        
        v_par_mag = abs(v_paralelo_escalar)
        v_perp_mag = v_perpendicular.magnitude()
        
        tolerance = 1e-12
        
        if v_perp_mag < tolerance:
            return "Rectilínea (v paralelo a B)"
        elif v_par_mag < tolerance:
            return "Circular (v perpendicular a B)"
        else:
            return f"Helicoidal (v_∥ = {v_par_mag:.3e} m/s, v_⊥ = {v_perp_mag:.3e} m/s)"
    
    def calcular_radio_preciso(self, m, v, q, B):
        """Cálculo preciso del radio de curvatura"""
        B_mag = B.magnitude()
        v_mag = v.magnitude()
        
        if B_mag < 1e-15 or abs(q) < 1e-30:
            return float('inf')
        
        # r = mv/(|q|B) - usar valor absoluto de la carga
        radio = (m * v_mag) / (abs(q) * B_mag)
        
        # Validar resultado
        if not math.isfinite(radio) or radio < 0:
            raise ValueError("Radio de curvatura inválido")
        
        return radio

    def resolver_con_precision(self):
        """Resuelve el problema con validación y precisión mejoradas"""
        texto = self.entrada.get(1.0, tk.END).strip()

        if not texto:
            messagebox.showwarning("⚠️ Advertencia", "Ingresa un problema para resolver")
            return

        try:
            # Extraer y validar datos
            datos = self.extraer_datos_mejorados(texto)

            # Calcular fuerzas con precisión
            F_E, F_B, F_total = self.calcular_fuerzas_precisas(datos)

            # Mostrar solución completa
            self.mostrar_solucion_mejorada(datos, F_E, F_B, F_total)

        except ValueError as e:
            messagebox.showerror("❌ Error de Validación", f"Error en los datos:\n{e}")
        except Exception as e:
            messagebox.showerror("❌ Error", f"Error inesperado:\n{e}")

    def mostrar_solucion_mejorada(self, datos, F_E, F_B, F_total):
        """Muestra solución con separación clara de vectores y escalares"""
        self.resultados.delete(1.0, tk.END)

        q, m = datos['q'], datos['m']
        v, E, B = datos['v'], datos['E'], datos['B']

        solucion = "=" * 80 + "\n"
        solucion += "🔬 SOLUCIÓN CON PRECISIÓN MEJORADA - SISTEMA INTERNACIONAL\n"
        solucion += "=" * 80 + "\n\n"

        # DATOS VALIDADOS
        solucion += "📋 DATOS VALIDADOS (SISTEMA SI):\n"
        solucion += f"   🔸 Partícula: {datos['tipo']}\n"
        solucion += f"   🔸 Carga: q = {q:.12e} C\n"
        solucion += f"   🔸 Masa: m = {m:.12e} kg\n"
        solucion += f"   🔸 Velocidad: v = {v} m/s\n"
        solucion += f"   🔸 |v| = {v.magnitude():.6e} m/s\n"
        solucion += f"   🔸 Campo eléctrico: E = {E} V/m\n"
        solucion += f"   🔸 |E| = {E.magnitude():.6e} V/m\n"
        solucion += f"   🔸 Campo magnético: B = {B} T\n"
        solucion += f"   🔸 |B| = {B.magnitude():.6e} T\n\n"

        # VALIDACIONES FÍSICAS
        solucion += "✅ VALIDACIONES FÍSICAS:\n"
        solucion += f"   • Velocidad < c: {v.magnitude():.2e} < {self.CONSTANTES['c']:.2e} ✓\n"
        solucion += f"   • Campos en rangos razonables ✓\n"
        solucion += f"   • Componentes vectoriales validadas ✓\n\n"

        # CÁLCULOS VECTORIALES PRECISOS
        solucion += "⚡ CÁLCULOS VECTORIALES (PRECISIÓN MEJORADA):\n\n"

        # Fuerza eléctrica
        solucion += "1️⃣ FUERZA ELÉCTRICA (F_E = qE):\n"
        solucion += f"   Vector: F_E = {F_E} N\n"
        solucion += f"   Magnitud: |F_E| = {F_E.magnitude():.12e} N\n"
        solucion += f"   Componentes: Fx = {F_E.x:.6e}, Fy = {F_E.y:.6e}, Fz = {F_E.z:.6e} N\n\n"

        # Producto cruz para fuerza magnética
        v_cross_B = v.cross(B)
        solucion += "2️⃣ FUERZA MAGNÉTICA (F_B = q(v × B)):\n"
        solucion += f"   Producto cruz: v × B = {v_cross_B} m·T/s\n"
        solucion += f"   Vector: F_B = {F_B} N\n"
        solucion += f"   Magnitud: |F_B| = {F_B.magnitude():.12e} N\n"
        solucion += f"   Componentes: Fx = {F_B.x:.6e}, Fy = {F_B.y:.6e}, Fz = {F_B.z:.6e} N\n\n"

        # Fuerza total
        solucion += "3️⃣ FUERZA TOTAL (SUPERPOSICIÓN LINEAL):\n"
        solucion += f"   Vector: F_total = F_E + F_B = {F_total} N\n"
        solucion += f"   Magnitud: |F_total| = {F_total.magnitude():.12e} N\n"
        solucion += f"   Componentes: Fx = {F_total.x:.6e}, Fy = {F_total.y:.6e}, Fz = {F_total.z:.6e} N\n\n"

        # Aceleración
        a = F_total * (1/m)
        solucion += "🚀 ACELERACIÓN RESULTANTE:\n"
        solucion += f"   Vector: a = F/m = {a} m/s²\n"
        solucion += f"   Magnitud: |a| = {a.magnitude():.6e} m/s²\n\n"

        # ANÁLISIS DE TRAYECTORIA PRECISO
        tipo_trayectoria = self.analizar_trayectoria_precisa(v, B)
        solucion += "🔄 ANÁLISIS DE TRAYECTORIA:\n"
        solucion += f"   Tipo: {tipo_trayectoria}\n"

        # Análisis vectorial detallado
        if B.magnitude() > 1e-15:
            B_unit = B.normalize()
            v_paralelo_escalar = v.dot(B_unit)
            v_paralelo = B_unit * v_paralelo_escalar
            v_perpendicular = v - v_paralelo

            solucion += f"   Descomposición de velocidad respecto a B:\n"
            solucion += f"   • v_∥ (paralelo): {v_paralelo} m/s, |v_∥| = {abs(v_paralelo_escalar):.6e} m/s\n"
            solucion += f"   • v_⊥ (perpendicular): {v_perpendicular} m/s, |v_⊥| = {v_perpendicular.magnitude():.6e} m/s\n"

            # Verificar ortogonalidad
            if abs(v_paralelo.dot(v_perpendicular)) < 1e-12:
                solucion += f"   ✓ Componentes ortogonales verificadas\n"

        solucion += "\n"

        # PARÁMETROS CICLOTRONICOS
        if B.magnitude() > 1e-15:
            try:
                radio = self.calcular_radio_preciso(m, v, q, B)
                omega_c = abs(q) * B.magnitude() / m  # Frecuencia angular ciclotrónica
                T_c = 2 * math.pi / omega_c  # Período
                f_c = 1 / T_c  # Frecuencia

                solucion += "🔵 PARÁMETROS CICLOTRONICOS (SIN CAMPO ELÉCTRICO):\n"
                solucion += f"   Radio de Larmor: r = mv/(|q|B) = {radio:.12e} m\n"
                solucion += f"   Radio en cm: r = {radio*100:.6f} cm\n"
                solucion += f"   Frecuencia angular: ωc = |q|B/m = {omega_c:.6e} rad/s\n"
                solucion += f"   Período ciclotrónico: T = 2π/ωc = {T_c:.6e} s\n"
                solucion += f"   Frecuencia: f = 1/T = {f_c:.6e} Hz\n\n"

                # Validar consistencia
                radio_check = (m * v.magnitude()) / (abs(q) * B.magnitude())
                if abs(radio - radio_check) < 1e-15:
                    solucion += f"   ✓ Cálculo de radio verificado\n\n"

            except Exception as e:
                solucion += f"   ⚠️ Error en cálculos ciclotronicos: {e}\n\n"

        # ANÁLISIS ENERGÉTICO
        v_mag = v.magnitude()
        K = 0.5 * m * v_mag * v_mag
        solucion += "⚡ ANÁLISIS ENERGÉTICO:\n"
        solucion += f"   Energía cinética: K = ½mv² = {K:.12e} J\n"
        solucion += f"   En eV: K = {K/self.CONSTANTES['e']:.6e} eV\n"
        solucion += f"   Velocidad relativista (v/c): {v_mag/self.CONSTANTES['c']:.6e}\n"

        if v_mag/self.CONSTANTES['c'] > 0.1:
            solucion += f"   ⚠️ Considerar efectos relativistas para v/c > 0.1\n"
        else:
            solucion += f"   ✓ Aproximación clásica válida\n"

        solucion += "\n"

        # VERIFICACIONES FINALES
        solucion += "🔍 VERIFICACIONES DE CONSISTENCIA:\n"

        # Verificar que F_B ⊥ v y F_B ⊥ B
        if v.magnitude() > 1e-15 and B.magnitude() > 1e-15:
            dot_FB_v = F_B.dot(v)
            dot_FB_B = F_B.dot(B)
            solucion += f"   • F_B · v = {dot_FB_v:.2e} (debe ser ≈ 0) "
            solucion += "✓\n" if abs(dot_FB_v) < 1e-12 else "❌\n"
            solucion += f"   • F_B · B = {dot_FB_B:.2e} (debe ser ≈ 0) "
            solucion += "✓\n" if abs(dot_FB_B) < 1e-12 else "❌\n"

        # Verificar conservación de energía (campo B no hace trabajo)
        if B.magnitude() > 1e-15:
            solucion += f"   • Campo magnético no realiza trabajo ✓\n"

        solucion += "\n✅ SOLUCIÓN COMPLETA CON VALIDACIÓN EXITOSA"

        self.resultados.insert(tk.END, solucion)

    def validar_unidades_texto(self):
        """Valida las unidades encontradas en el texto"""
        texto = self.entrada.get(1.0, tk.END).strip()

        if not texto:
            messagebox.showwarning("⚠️ Advertencia", "Ingresa texto para validar")
            return

        try:
            # Buscar unidades en el texto
            unidades_encontradas = []

            # Patrones para diferentes unidades
            patrones_unidades = {
                'velocidad': r'(m/s|km/s|km/h)',
                'campo_E': r'(V/m|kV/m|N/C)',
                'campo_B': r'(T|mT|μT|G|mG)',
                'carga': r'(C|e|mC|μC|nC)',
                'masa': r'(kg|g|u|MeV/c²)'
            }

            for tipo, patron in patrones_unidades.items():
                matches = re.findall(patron, texto, re.IGNORECASE)
                if matches:
                    unidades_encontradas.append(f"{tipo}: {', '.join(set(matches))}")

            if unidades_encontradas:
                mensaje = "Unidades encontradas:\n" + "\n".join(unidades_encontradas)
                mensaje += "\n\n✅ Todas son compatibles con el Sistema Internacional"
                messagebox.showinfo("🔍 Validación de Unidades", mensaje)
            else:
                messagebox.showwarning("⚠️ Unidades", "No se encontraron unidades explícitas en el texto")

        except Exception as e:
            messagebox.showerror("❌ Error", f"Error en validación: {e}")

    def limpiar(self):
        """Limpia las áreas de texto"""
        self.entrada.delete(1.0, tk.END)
        self.resultados.delete(1.0, tk.END)

    def ejecutar(self):
        """Ejecuta la aplicación"""
        self.ventana.mainloop()

def main():
    """Función principal"""
    try:
        print("🚀 Iniciando Calculadora de Lorentz MEJORADA...")
        app = CalculadoraLorentzMejorada()
        app.ejecutar()
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Presiona Enter para salir...")

if __name__ == "__main__":
    main()
