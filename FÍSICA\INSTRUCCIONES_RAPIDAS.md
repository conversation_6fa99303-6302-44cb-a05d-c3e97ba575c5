# 🚀 Calculadora de Fuerza de Lorentz - Inicio Rápido

## ⚡ Ejecución Inmediata

### Opción 1: Programa Simple (RECOMENDADO)
```bash
python calculadora_lorentz.py
```

### Opción 2: Verificar Si<PERSON>
```bash
python test_simple.py
```

### Opción 3: Inicio <PERSON>
```bash
python iniciar_simple.py
```

## 📝 Cómo Usar

1. **Ejecuta** el programa
2. **Copia y pega** tu problema de física en el área de texto
3. **Presiona** el botón "🧮 RESOLVER"
4. **Obtén** la solución completa

## 🎯 Ejemplo de Uso

### Texto de Entrada:
```
Un protón con velocidad v = (2 × 10⁵) î m/s se mueve en campos 
E = (3 × 10³) ĵ V/m y B = (0.02) k̂ T. Calcula todas las fuerzas.
```

### Resultado Automático:
- ✅ Partícula: Protón identificado
- ✅ Fuerza eléctrica: F_E = (0, 4.81e-16, 0) N
- ✅ Fuerza magnética: F_B = (0, -6.41e-16, 0) N  
- ✅ Fuerza total: F_total = (0, -1.60e-16, 0) N
- ✅ Aceleración: a = (0, -9.58e10, 0) m/s²
- ✅ Radio sin campo E: r = 1.04e-2 m

## 🔧 Formatos Soportados

### Vectores:
- `v = (2 × 10⁵) î m/s` ← Con vectores unitarios
- `E = (0, 1000, 0) V/m` ← Con paréntesis
- `B = 0.1 T` ← Valor simple (asume dirección X)

### Partículas:
- "protón" o "proton" → Carga +e, masa mp
- "electrón" o "electron" → Carga -e, masa me
- Cualquier otra → Usa valores por defecto

### Notación Científica:
- `2 × 10⁵` → Se convierte a `2e5`
- `1.6 × 10⁻¹⁹` → Se convierte a `1.6e-19`
- `3 * 10^3` → Se convierte a `3e3`

## 🎓 Ejemplos Adicionales

### Ejemplo 1: Electrón en Campo Magnético
```
Un electrón con velocidad 1e6 m/s entra en campo magnético 0.1 T. 
Calcula el radio de la órbita.
```

### Ejemplo 2: Movimiento Helicoidal
```
Partícula con carga 1.6e-19 C, masa 1.67e-27 kg, velocidad (1e5, 1e5, 0) m/s 
en campo magnético (0, 0, 0.5) T.
```

### Ejemplo 3: Campos Cruzados
```
Protón con velocidad (1e5, 0, 0) m/s en campos E = (0, 1000, 0) V/m 
y B = (0, 0, 0.01) T.
```

## 🔍 Qué Calcula el Programa

### Fuerzas:
- **Fuerza Eléctrica**: F_E = qE
- **Fuerza Magnética**: F_B = q(v × B)
- **Fuerza Total**: F_total = F_E + F_B

### Cinemática:
- **Aceleración**: a = F/m
- **Radio de curvatura**: r = mv/(qB)
- **Período ciclotrónico**: T = 2πm/(qB)

### Energía:
- **Energía cinética**: K = ½mv²
- **Conversión a eV**: K_eV = K/e

## ⚠️ Solución de Problemas

### Problema: "No se ejecuta el programa"
**Solución:**
- Verifica que tengas Python instalado
- Ejecuta: `python --version`
- Si usas Linux: `sudo apt-get install python3-tk`

### Problema: "No reconoce los datos"
**Solución:**
- Usa formato claro: `v = (2e5) î m/s`
- Incluye unidades: V/m, T, m/s
- Especifica la partícula: "protón" o "electrón"

### Problema: "Resultados incorrectos"
**Solución:**
- Verifica que los números estén en notación correcta
- Revisa que las direcciones de los vectores sean correctas
- Comprueba las unidades (SI: metros, segundos, etc.)

## 📚 Casos de Uso Típicos

### Para Estudiantes:
- ✅ Resolver tareas de física
- ✅ Verificar cálculos manuales
- ✅ Prepararse para exámenes
- ✅ Entender conceptos visualizando resultados

### Para Profesores:
- ✅ Generar soluciones rápidas
- ✅ Verificar ejercicios propuestos
- ✅ Crear ejemplos para clase
- ✅ Demostrar cálculos en tiempo real

## 🎯 Limitaciones

- Solo campos uniformes y estáticos
- Física clásica (no relativista)
- Texto en español/inglés básico
- Requiere datos explícitos en el problema

## 🚀 Próximas Versiones

- Reconocimiento de imágenes (OCR)
- Más tipos de partículas
- Efectos relativistas
- Exportación a PDF

---

## 🆘 Ayuda Rápida

**¿No funciona?** Ejecuta:
```bash
python test_simple.py
```

**¿Necesitas más funciones?** Usa:
```bash
python fuerza_lorentz_main.py
```

**¿Quieres la guía completa?** Lee:
```
README.md
GUIA_ENTRADA_TEXTO.md
```

---

**¡Disfruta resolviendo problemas de física!** ⚡🔬🎯
