"""
Calculadora de Fuerza de Lorentz - Versión Simple y Autocontenida
Interfaz gráfica sencilla con entrada de texto
"""

import tkinter as tk
from tkinter import scrolledtext, messagebox
import re
import math

class CalculadoraLorentz:
    def __init__(self):
        # Constantes físicas
        self.e = 1.602176634e-19  # Carga elemental (C)
        self.me = 9.1093837015e-31  # Masa electrón (kg)
        self.mp = 1.67262192369e-27  # Masa protón (kg)
        
        # Crear ventana principal
        self.ventana = tk.Tk()
        self.ventana.title("🔬 Calculadora de Fuerza de Lorentz")
        self.ventana.geometry("800x600")
        self.ventana.configure(bg='lightblue')
        
        self.crear_interfaz()
    
    def crear_interfaz(self):
        # Título
        titulo = tk.Label(self.ventana, text="⚡ CALCULADORA DE FUERZA DE LORENTZ ⚡", 
                         font=('Arial', 16, 'bold'), bg='lightblue', fg='darkblue')
        titulo.pack(pady=10)
        
        # Instrucciones
        instrucciones = tk.Label(self.ventana, 
                               text="📝 Copia y pega aquí tu problema de física:",
                               font=('Arial', 12), bg='lightblue')
        instrucciones.pack(pady=5)
        
        # Área de entrada de texto
        self.entrada = scrolledtext.ScrolledText(self.ventana, height=6, width=90, 
                                               font=('Arial', 11), wrap=tk.WORD)
        self.entrada.pack(padx=20, pady=10)
        
        # Ejemplo por defecto
        ejemplo = """Un protón con velocidad v = (2 × 10⁵) î m/s se mueve en campos E = (3 × 10³) ĵ V/m y B = (0.02) k̂ T. Calcula todas las fuerzas."""
        self.entrada.insert(tk.END, ejemplo)
        
        # Botones
        frame_botones = tk.Frame(self.ventana, bg='lightblue')
        frame_botones.pack(pady=10)
        
        btn_resolver = tk.Button(frame_botones, text="🧮 RESOLVER", 
                               command=self.resolver, font=('Arial', 12, 'bold'),
                               bg='green', fg='white', padx=20, pady=5)
        btn_resolver.pack(side=tk.LEFT, padx=10)
        
        btn_limpiar = tk.Button(frame_botones, text="🗑️ LIMPIAR", 
                              command=self.limpiar, font=('Arial', 12),
                              bg='red', fg='white', padx=20, pady=5)
        btn_limpiar.pack(side=tk.LEFT, padx=10)
        
        # Área de resultados
        tk.Label(self.ventana, text="📊 RESULTADOS:", 
                font=('Arial', 12, 'bold'), bg='lightblue').pack(anchor='w', padx=20, pady=(10,5))
        
        self.resultados = scrolledtext.ScrolledText(self.ventana, height=12, width=90, 
                                                  font=('Courier', 10), wrap=tk.WORD)
        self.resultados.pack(padx=20, pady=10, fill=tk.BOTH, expand=True)
    
    def extraer_datos(self, texto):
        """Extrae datos del texto del problema"""
        # Convertir notación científica
        texto = re.sub(r'(\d+(?:\.\d+)?)\s*×\s*10\^?([+-]?\d+)', r'\1e\2', texto)
        texto = re.sub(r'(\d+(?:\.\d+)?)\s*\*\s*10\^?([+-]?\d+)', r'\1e\2', texto)
        
        datos = {}
        
        # Identificar partícula
        if 'protón' in texto.lower() or 'proton' in texto.lower():
            datos['tipo'] = 'protón'
            datos['q'] = self.e
            datos['m'] = self.mp
        elif 'electrón' in texto.lower() or 'electron' in texto.lower():
            datos['tipo'] = 'electrón'
            datos['q'] = -self.e
            datos['m'] = self.me
        else:
            datos['tipo'] = 'partícula'
            datos['q'] = self.e
            datos['m'] = self.mp
        
        # Extraer velocidad
        datos['v'] = self.extraer_vector(texto, 'v')
        
        # Extraer campos
        datos['E'] = self.extraer_vector(texto, 'E')
        datos['B'] = self.extraer_vector(texto, 'B')
        
        return datos
    
    def extraer_vector(self, texto, var):
        """Extrae un vector del texto"""
        # Buscar formato con î, ĵ, k̂
        patron_i = rf'{var}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[îi]'
        patron_j = rf'{var}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[ĵj]'
        patron_k = rf'{var}[^=]*=.*?([+-]?\d+(?:\.\d+)?(?:e[+-]?\d+)?)[^îĵk]*[k̂k]'
        
        x = self.buscar_numero(texto, patron_i)
        y = self.buscar_numero(texto, patron_j)
        z = self.buscar_numero(texto, patron_k)
        
        # Si no encuentra nada, buscar formato (x, y, z)
        if x == 0 and y == 0 and z == 0:
            patron = rf'{var}[^=]*=\s*\(([^)]+)\)'
            match = re.search(patron, texto, re.IGNORECASE)
            if match:
                partes = match.group(1).split(',')
                if len(partes) >= 3:
                    try:
                        x = float(re.sub(r'[^\d.e+-]', '', partes[0]))
                        y = float(re.sub(r'[^\d.e+-]', '', partes[1]))
                        z = float(re.sub(r'[^\d.e+-]', '', partes[2]))
                    except:
                        pass
        
        return [x, y, z]
    
    def buscar_numero(self, texto, patron):
        """Busca un número usando un patrón"""
        match = re.search(patron, texto, re.IGNORECASE)
        if match:
            try:
                return float(match.group(1))
            except:
                return 0
        return 0
    
    def calcular_fuerzas(self, datos):
        """Calcula todas las fuerzas"""
        q = datos['q']
        v = datos['v']
        E = datos['E']
        B = datos['B']
        
        # Fuerza eléctrica: F_E = qE
        F_E = [q * E[0], q * E[1], q * E[2]]
        
        # Fuerza magnética: F_B = q(v × B)
        vxB = [
            v[1]*B[2] - v[2]*B[1],
            v[2]*B[0] - v[0]*B[2],
            v[0]*B[1] - v[1]*B[0]
        ]
        F_B = [q * vxB[0], q * vxB[1], q * vxB[2]]
        
        # Fuerza total
        F_total = [F_E[0] + F_B[0], F_E[1] + F_B[1], F_E[2] + F_B[2]]
        
        return F_E, F_B, F_total
    
    def magnitud(self, vector):
        """Calcula la magnitud de un vector"""
        return math.sqrt(vector[0]**2 + vector[1]**2 + vector[2]**2)
    
    def resolver(self):
        """Resuelve el problema"""
        texto = self.entrada.get(1.0, tk.END).strip()
        
        if not texto:
            messagebox.showwarning("Advertencia", "Ingresa un problema para resolver")
            return
        
        try:
            # Extraer datos
            datos = self.extraer_datos(texto)
            
            # Calcular fuerzas
            F_E, F_B, F_total = self.calcular_fuerzas(datos)
            
            # Mostrar resultados
            self.mostrar_solucion(datos, F_E, F_B, F_total)
            
        except Exception as e:
            messagebox.showerror("Error", f"Error al resolver: {e}")
    
    def mostrar_solucion(self, datos, F_E, F_B, F_total):
        """Muestra la solución completa"""
        self.resultados.delete(1.0, tk.END)
        
        resultado = "=" * 60 + "\n"
        resultado += "🔬 SOLUCIÓN DEL PROBLEMA\n"
        resultado += "=" * 60 + "\n\n"
        
        # Datos
        resultado += "📋 DATOS IDENTIFICADOS:\n"
        resultado += f"   Partícula: {datos['tipo']}\n"
        resultado += f"   Carga: {datos['q']:.3e} C\n"
        resultado += f"   Masa: {datos['m']:.3e} kg\n"
        resultado += f"   Velocidad: ({datos['v'][0]:.0f}, {datos['v'][1]:.0f}, {datos['v'][2]:.0f}) m/s\n"
        resultado += f"   Campo E: ({datos['E'][0]:.0f}, {datos['E'][1]:.0f}, {datos['E'][2]:.0f}) V/m\n"
        resultado += f"   Campo B: ({datos['B'][0]:.3f}, {datos['B'][1]:.3f}, {datos['B'][2]:.3f}) T\n\n"
        
        # Fuerzas
        resultado += "⚡ CÁLCULO DE FUERZAS:\n\n"
        
        resultado += f"1️⃣ Fuerza Eléctrica (F_E = qE):\n"
        resultado += f"   F_E = ({F_E[0]:.3e}, {F_E[1]:.3e}, {F_E[2]:.3e}) N\n"
        resultado += f"   |F_E| = {self.magnitud(F_E):.3e} N\n\n"
        
        resultado += f"2️⃣ Fuerza Magnética (F_B = q(v×B)):\n"
        resultado += f"   F_B = ({F_B[0]:.3e}, {F_B[1]:.3e}, {F_B[2]:.3e}) N\n"
        resultado += f"   |F_B| = {self.magnitud(F_B):.3e} N\n\n"
        
        resultado += f"3️⃣ Fuerza Total (F = F_E + F_B):\n"
        resultado += f"   F_total = ({F_total[0]:.3e}, {F_total[1]:.3e}, {F_total[2]:.3e}) N\n"
        resultado += f"   |F_total| = {self.magnitud(F_total):.3e} N\n\n"
        
        # Aceleración
        m = datos['m']
        a = [F_total[0]/m, F_total[1]/m, F_total[2]/m]
        resultado += f"🚀 ACELERACIÓN:\n"
        resultado += f"   a = F/m = ({a[0]:.3e}, {a[1]:.3e}, {a[2]:.3e}) m/s²\n"
        resultado += f"   |a| = {self.magnitud(a):.3e} m/s²\n\n"
        
        # Radio de curvatura si hay campo B
        mag_B = self.magnitud(datos['B'])
        if mag_B > 0:
            mag_v = self.magnitud(datos['v'])
            radio = (datos['m'] * mag_v) / (abs(datos['q']) * mag_B)
            periodo = (2 * math.pi * datos['m']) / (abs(datos['q']) * mag_B)
            
            resultado += f"🔵 MOVIMIENTO EN CAMPO MAGNÉTICO:\n"
            resultado += f"   Radio de curvatura: r = {radio:.3e} m\n"
            resultado += f"   Período: T = {periodo:.3e} s\n"
            resultado += f"   Frecuencia: f = {1/periodo:.3e} Hz\n\n"
            
            if self.magnitud(datos['E']) > 0:
                resultado += f"📌 Sin campo eléctrico:\n"
                resultado += f"   Radio circular: {radio:.3e} m\n"
        
        # Energía
        mag_v = self.magnitud(datos['v'])
        energia = 0.5 * datos['m'] * mag_v**2
        resultado += f"⚡ ENERGÍA CINÉTICA:\n"
        resultado += f"   K = ½mv² = {energia:.3e} J\n"
        resultado += f"   K = {energia/self.e:.3e} eV\n"
        
        self.resultados.insert(tk.END, resultado)
    
    def limpiar(self):
        """Limpia las áreas de texto"""
        self.entrada.delete(1.0, tk.END)
        self.resultados.delete(1.0, tk.END)
    
    def ejecutar(self):
        """Ejecuta la aplicación"""
        self.ventana.mainloop()

# Función principal
def main():
    try:
        app = CalculadoraLorentz()
        app.ejecutar()
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
